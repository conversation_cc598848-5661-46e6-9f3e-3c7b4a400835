const mongoose = require('mongoose');

// MongoDB connection
const MONGODB_URI = "mongodb+srv://infernogane:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";

// Define schemas
const ExamResultSchema = new mongoose.Schema({
  studentEmail: String,
  subject: String,
  examType: String,
  marks: Number,
  semester: Number,
  createdAt: { type: Date, default: Date.now },
});

// Create models
const ExamResult = mongoose.model('ExamResult', ExamResultSchema);

async function updateMarks() {
  try {
    console.log('Starting marks update process...');
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get all students
    const db = mongoose.connection.db;
    const students = await db.collection('students').find({}).toArray();

    if (students.length === 0) {
      console.log('No students found in the database');
      return;
    }

    console.log(`Found ${students.length} students`);

    // For each student, add an exam result with 14 marks
    for (const student of students) {
      // Check if the student already has an exam result with 14 marks
      const existingResult = await ExamResult.findOne({
        studentEmail: student.email,
        marks: 14
      });

      if (existingResult) {
        console.log(`Student ${student.email} already has an exam result with 14 marks`);
        continue;
      }

      // Create a new exam result with 14 marks
      const examResult = new ExamResult({
        studentEmail: student.email,
        subject: 'Mathematics',
        examType: 'CAT 1',
        marks: 14,
        semester: student.semester || 1,
      });

      await examResult.save();
      console.log(`Added exam result with 14 marks for student ${student.email}`);
    }

    console.log('Marks updated successfully');
  } catch (error) {
    console.error('Error updating marks:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
}

// Run the function
updateMarks();
