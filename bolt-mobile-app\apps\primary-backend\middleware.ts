import type { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";

export function authMiddleware(req: Request, res: Response, next: NextFunction) {
   const authHeader=req.headers.authorization;
    const token = authHeader && authHeader.split(" ") [1];//  bearer token
    if (!token) {
        res.status(401).json({message: "Unauthorized"})
        return ;
    }

    const decoded = jwt.verify(token,process.env.JWT_PUBLIC_KEY!, {
        algorithms: ["RS256"],
    });
    if (!decoded) {
        res.status (401) . json({ message: "Unauthorized" }) 
        return ;
    }
    console.log(decoded);
    const userId = (decoded as any).sub;
    if (!userId) {
        res.status(401).json({message: "Unauthorized"})
        return ;
    }
    req.userId = userId;
    next();
}
