import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { getToken } from 'next-auth/jwt';
import connectToDatabase from '@/lib/mongodb';
import Faculty from '@/models/Faculty';
import { handler as authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function GET(req: NextRequest) {
  try {
    // Try both methods to get the session
    let session = await getServerSession(authOptions);
    
    // If session is not available, try to get the token directly
    if (!session || !session.user) {
      const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET || 'your-secret-key' });
      
      if (!token || !token.email || token.role !== 'faculty') {
        console.error('Authentication failed: No valid faculty session or token found');
        return NextResponse.json(
          { message: 'Unauthorized' },
          { status: 401 }
        );
      }
      
      // Create a session-like object from the token
      session = {
        user: {
          email: token.email as string,
          id: token.id as string,
          role: 'faculty',
        }
      };
    } else if (session.user.role !== 'faculty') {
      return NextResponse.json(
        { message: 'Unauthorized: Not a faculty member' },
        { status: 403 }
      );
    }

    await connectToDatabase();

    // Get faculty profile
    const faculty = await Faculty.findOne({ email: session.user.email });
    
    if (!faculty) {
      return NextResponse.json(
        { message: 'Faculty profile not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      _id: faculty._id,
      fullName: faculty.fullName,
      email: faculty.email,
      department: faculty.department,
      subjects: faculty.subjects || [],
    });
  } catch (error) {
    console.error('Faculty profile fetch error:', error);
    return NextResponse.json(
      { message: 'Error fetching faculty profile' },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    // Try both methods to get the session
    let session = await getServerSession(authOptions);
    
    // If session is not available, try to get the token directly
    if (!session || !session.user) {
      const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET || 'your-secret-key' });
      
      if (!token || !token.email || token.role !== 'faculty') {
        console.error('Authentication failed: No valid faculty session or token found');
        return NextResponse.json(
          { message: 'Unauthorized' },
          { status: 401 }
        );
      }
      
      // Create a session-like object from the token
      session = {
        user: {
          email: token.email as string,
          id: token.id as string,
          role: 'faculty',
        }
      };
    } else if (session.user.role !== 'faculty') {
      return NextResponse.json(
        { message: 'Unauthorized: Not a faculty member' },
        { status: 403 }
      );
    }

    const { fullName, department, subjects } = await req.json();

    // Validate input
    if (!fullName || !department) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    await connectToDatabase();

    // Update faculty profile
    const updatedFaculty = await Faculty.findOneAndUpdate(
      { email: session.user.email },
      { 
        fullName,
        department,
        subjects: subjects || [],
      },
      { new: true }
    );

    if (!updatedFaculty) {
      return NextResponse.json(
        { message: 'Faculty profile not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      _id: updatedFaculty._id,
      fullName: updatedFaculty.fullName,
      email: updatedFaculty.email,
      department: updatedFaculty.department,
      subjects: updatedFaculty.subjects || [],
    });
  } catch (error) {
    console.error('Faculty profile update error:', error);
    return NextResponse.json(
      { message: 'Error updating faculty profile' },
      { status: 500 }
    );
  }
}
