'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';

export default function FacultyDashboardPage() {
  const { data: session } = useSession();
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalSubjects: 0,
    pendingRequests: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        
        // Fetch dashboard statistics
        const response = await fetch('/api/faculty/dashboard-stats');
        
        if (response.ok) {
          const data = await response.json();
          setStats(data);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };
    
    if (session?.user) {
      fetchDashboardData();
    }
  }, [session]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-gray-500">Loading dashboard data...</p>
      </div>
    );
  }

  return (
    <div>
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Faculty Dashboard</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">
                Total Students
              </dt>
              <dd className="mt-1 text-3xl font-semibold text-gray-900">
                {stats.totalStudents}
              </dd>
            </dl>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <Link href="/faculty/dashboard/students" className="font-medium text-indigo-600 hover:text-indigo-500">
                View all students
              </Link>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">
                Total Subjects
              </dt>
              <dd className="mt-1 text-3xl font-semibold text-gray-900">
                {stats.totalSubjects}
              </dd>
            </dl>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <Link href="/faculty/dashboard/marks" className="font-medium text-indigo-600 hover:text-indigo-500">
                Manage marks
              </Link>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">
                Special Class Requests
              </dt>
              <dd className="mt-1 text-3xl font-semibold text-gray-900">
                {stats.pendingRequests}
              </dd>
            </dl>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <Link href="/faculty/dashboard/requests" className="font-medium text-indigo-600 hover:text-indigo-500">
                View requests
              </Link>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Quick Actions
          </h3>
        </div>
        <div className="border-t border-gray-200">
          <div className="bg-gray-50 px-4 py-5 grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg shadow">
              <div>
                <span className="rounded-lg inline-flex p-3 bg-indigo-50 text-indigo-700">
                  <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium">
                  <Link href="/faculty/dashboard/marks/upload" className="focus:outline-none">
                    <span className="absolute inset-0" aria-hidden="true"></span>
                    Upload Marks
                  </Link>
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  Upload marks for students in your subjects
                </p>
              </div>
            </div>
            
            <div className="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg shadow">
              <div>
                <span className="rounded-lg inline-flex p-3 bg-indigo-50 text-indigo-700">
                  <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium">
                  <Link href="/faculty/dashboard/students" className="focus:outline-none">
                    <span className="absolute inset-0" aria-hidden="true"></span>
                    View Student Details
                  </Link>
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  View and manage student information
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
