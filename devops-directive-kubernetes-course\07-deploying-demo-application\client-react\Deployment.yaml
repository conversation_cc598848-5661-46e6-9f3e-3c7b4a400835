apiVersion: apps/v1
kind: Deployment
metadata:
  name: client-react-nginx
  namespace: demo-app
  labels:
    app: client-react-nginx
spec:
  replicas: 1
  selector:
    matchLabels:
      app: client-react-nginx
  template:
    metadata:
      labels:
        app: client-react-nginx
    spec:
      containers:
        - image: sidpalas/devops-directive-docker-course-client-react-nginx:foobarbaz
          name: client-react-nginx
          ports:
            - containerPort: 8080
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /ping
              port: 8080
          resources:
            limits:
              memory: 100Mi
            requests:
              cpu: 50m
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
          volumeMounts:
            - mountPath: /etc/nginx/conf.d
              name: nginx-conf
      securityContext:
        seccompProfile:
          type: RuntimeDefault
      volumes:
        - configMap:
            defaultMode: 420
            name: nginx-conf
          name: nginx-conf
