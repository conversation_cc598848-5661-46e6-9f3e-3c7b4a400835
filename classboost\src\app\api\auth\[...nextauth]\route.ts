import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import bcrypt from 'bcryptjs';
import connectToDatabase from '@/lib/mongodb';
import Student from '@/models/Student';
import Faculty from '@/models/Faculty';

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Please enter email and password');
        }

        await connectToDatabase();

        // Check if the user is a faculty member
        const faculty = await Faculty.findOne({ email: credentials.email });

        if (faculty) {
          const isPasswordCorrect = await bcrypt.compare(
            credentials.password,
            faculty.password
          );

          if (!isPasswordCorrect) {
            throw new Error('Invalid password');
          }

          return {
            id: faculty._id.toString(),
            email: faculty.email,
            name: faculty.fullName,
            role: 'faculty',
            department: faculty.department,
          };
        }

        // If not faculty, check if the user is a student
        const student = await Student.findOne({ email: credentials.email });

        if (!student) {
          throw new Error('No user found with this email');
        }

        const isPasswordCorrect = await bcrypt.compare(
          credentials.password,
          student.password
        );

        if (!isPasswordCorrect) {
          throw new Error('Invalid password');
        }

        return {
          id: student._id.toString(),
          email: student.email,
          name: student.fullName,
          role: 'student',
          rollNumber: student.rollNumber,
          profileCompleted: student.profileCompleted,
        };
      }
    })
  ],
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;

        // Add role-specific properties
        if (user.role === 'student') {
          token.rollNumber = user.rollNumber;
          token.profileCompleted = user.profileCompleted;
        } else if (user.role === 'faculty') {
          token.department = user.department;
        }
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id;
        session.user.role = token.role;

        // Add role-specific properties to the session
        if (token.role === 'student') {
          session.user.rollNumber = token.rollNumber;
          session.user.profileCompleted = token.profileCompleted;
        } else if (token.role === 'faculty') {
          session.user.department = token.department;
        }
      }
      return session;
    },
  },
  pages: {
    signIn: '/login',
  },
  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key',
});

export { handler as GET, handler as POST };
