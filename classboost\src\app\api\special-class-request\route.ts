import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import connectToDatabase from '@/lib/mongodb';
import SpecialClassRequest from '@/models/SpecialClassRequest';
import { handler as authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { subject, reason } = await req.json();

    // Validate input
    if (!subject || !reason) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    await connectToDatabase();

    // Create new special class request
    const request = await SpecialClassRequest.create({
      studentEmail: session.user.email,
      subject,
      reason,
      status: 'pending',
    });

    return NextResponse.json(
      {
        message: 'Special class request submitted successfully',
        request: {
          id: request._id.toString(),
          subject: request.subject,
          reason: request.reason,
          status: request.status,
          createdAt: request.createdAt,
        }
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Special class request error:', error);
    return NextResponse.json(
      { message: 'Error submitting special class request' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectToDatabase();

    // Get all special class requests for the current student
    const requests = await SpecialClassRequest.find({
      studentEmail: session.user.email
    }).sort({ createdAt: -1 });

    return NextResponse.json(requests);
  } catch (error) {
    console.error('Special class requests fetch error:', error);
    return NextResponse.json(
      { message: 'Error fetching special class requests' },
      { status: 500 }
    );
  }
}
