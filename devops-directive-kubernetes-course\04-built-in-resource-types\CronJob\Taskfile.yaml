version: "3"

env:
  BORDER: double
  BORDER_FOREGROUND: "212"
  PADDING: "1 1"
  MARGIN: "1 1"
  NAMESPACE: 04--cronjob

tasks:
  01-create-namespace:
    desc: "Create a namespace for these examples and set as default"
    cmds:
      - kubectl apply -f Namespace.yaml
      - kubens ${NAMESPACE}

  02-apply-minimal:
    desc: "Apply the minimal CronJob configuration"
    cmds:
      - kubectl apply -f CronJob.echo-date-minimal.yaml

  03-apply-better:
    desc: "Apply the better CronJob configuration"
    cmds:
      - kubectl apply -f CronJob.echo-date-better.yaml

  04-create-job-from-cronjob:
    desc: "Manually create a job using a cronjob as the template"
    cmds:
      - kubectl create job --from=cronjob/echo-date-better manually-triggered

  05-delete-namespace:
    desc: "Delete the namespace to clean up"
    cmds:
      - cmd: gum style "🚨 Deleting the namespace recursively deletes the resources inside of it! 🚨 "
        silent: true
      - kubectl delete -f Namespace.yaml
