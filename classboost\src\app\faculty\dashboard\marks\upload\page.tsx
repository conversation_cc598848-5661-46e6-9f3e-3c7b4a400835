'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface Student {
  _id: string;
  fullName: string;
  email: string;
  rollNumber: string;
  department: string;
  semester: number;
}

interface MarkEntry {
  studentId: string;
  marks: string;
}

export default function UploadMarksPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [students, setStudents] = useState<Student[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const [subject, setSubject] = useState('');
  const [examType, setExamType] = useState('');
  const [semester, setSemester] = useState('');
  const [department, setDepartment] = useState('');
  const [markEntries, setMarkEntries] = useState<MarkEntry[]>([]);

  useEffect(() => {
    const fetchStudents = async () => {
      try {
        setLoading(true);
        setError('');
        
        const response = await fetch('/api/faculty/students');
        
        if (!response.ok) {
          throw new Error('Failed to fetch students');
        }
        
        const data = await response.json();
        setStudents(data);
      } catch (error: any) {
        console.error('Error fetching students:', error);
        setError(error.message || 'An error occurred while fetching students');
      } finally {
        setLoading(false);
      }
    };
    
    if (session?.user) {
      fetchStudents();
    }
  }, [session]);

  // Get unique departments and semesters for filters
  const departments = [...new Set(students.map(student => student.department))];
  const semesters = [...new Set(students.map(student => student.semester))].sort((a, b) => a - b);

  // Filter students when department or semester changes
  useEffect(() => {
    if (department && semester) {
      const filtered = students.filter(student => 
        student.department === department && 
        student.semester === parseInt(semester, 10)
      );
      setFilteredStudents(filtered);
      
      // Initialize mark entries for filtered students
      const initialMarkEntries = filtered.map(student => ({
        studentId: student._id,
        marks: '',
      }));
      setMarkEntries(initialMarkEntries);
    } else {
      setFilteredStudents([]);
      setMarkEntries([]);
    }
  }, [department, semester, students]);

  const handleMarkChange = (studentId: string, marks: string) => {
    // Validate marks (only numbers between 0-100)
    if (marks !== '' && (isNaN(Number(marks)) || Number(marks) < 0 || Number(marks) > 100)) {
      return;
    }
    
    setMarkEntries(prevEntries => 
      prevEntries.map(entry => 
        entry.studentId === studentId ? { ...entry, marks } : entry
      )
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!subject || !examType || !semester || !department) {
      setError('Please fill in all fields');
      return;
    }
    
    // Validate marks
    const invalidMarks = markEntries.some(entry => 
      entry.marks !== '' && (isNaN(Number(entry.marks)) || Number(entry.marks) < 0 || Number(entry.marks) > 100)
    );
    
    if (invalidMarks) {
      setError('Please enter valid marks (0-100) for all students');
      return;
    }
    
    // Filter out empty marks
    const validMarkEntries = markEntries.filter(entry => entry.marks !== '');
    
    if (validMarkEntries.length === 0) {
      setError('Please enter marks for at least one student');
      return;
    }
    
    try {
      setSubmitting(true);
      setError('');
      setSuccess('');
      
      // Prepare data for submission
      const markData = validMarkEntries.map(entry => {
        const student = students.find(s => s._id === entry.studentId);
        return {
          studentId: entry.studentId,
          studentEmail: student?.email,
          marks: Number(entry.marks),
          subject,
          examType,
          semester: parseInt(semester, 10),
        };
      });
      
      const response = await fetch('/api/faculty/marks/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ marks: markData }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to upload marks');
      }
      
      setSuccess('Marks uploaded successfully!');
      
      // Reset form after successful submission
      setTimeout(() => {
        router.push('/faculty/dashboard/marks');
      }, 2000);
    } catch (error: any) {
      console.error('Error uploading marks:', error);
      setError(error.message || 'An error occurred while uploading marks');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-gray-500">Loading...</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Upload Marks</h1>
        <Link
          href="/faculty/dashboard/marks"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200"
        >
          Back to Marks
        </Link>
      </div>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error}</span>
        </div>
      )}
      
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <strong className="font-bold">Success!</strong>
          <span className="block sm:inline"> {success}</span>
        </div>
      )}
      
      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="subject" className="block text-sm font-medium text-gray-700">Subject</label>
              <input
                type="text"
                id="subject"
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="e.g. Mathematics, Physics"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                required
              />
            </div>
            
            <div>
              <label htmlFor="examType" className="block text-sm font-medium text-gray-700">Exam Type</label>
              <select
                id="examType"
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                value={examType}
                onChange={(e) => setExamType(e.target.value)}
                required
              >
                <option value="">Select Exam Type</option>
                <option value="CAT 1">CAT 1</option>
                <option value="CAT 2">CAT 2</option>
                <option value="Final">Final</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="department" className="block text-sm font-medium text-gray-700">Department</label>
              <select
                id="department"
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                value={department}
                onChange={(e) => setDepartment(e.target.value)}
                required
              >
                <option value="">Select Department</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label htmlFor="semester" className="block text-sm font-medium text-gray-700">Semester</label>
              <select
                id="semester"
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                value={semester}
                onChange={(e) => setSemester(e.target.value)}
                required
              >
                <option value="">Select Semester</option>
                {semesters.map(sem => (
                  <option key={sem} value={sem}>{sem}</option>
                ))}
              </select>
            </div>
          </div>
          
          {filteredStudents.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Roll Number
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Marks (0-100)
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredStudents.map((student) => {
                    const markEntry = markEntries.find(entry => entry.studentId === student._id);
                    return (
                      <tr key={student._id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{student.rollNumber || 'N/A'}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{student.fullName}</div>
                          <div className="text-sm text-gray-500">{student.email}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="text"
                            className="block w-24 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="Marks"
                            value={markEntry?.marks || ''}
                            onChange={(e) => handleMarkChange(student._id, e.target.value)}
                          />
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          ) : (
            department && semester ? (
              <div className="text-center py-4 text-gray-500">
                No students found for the selected department and semester
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">
                Please select a department and semester to view students
              </div>
            )
          )}
          
          <div className="mt-6 flex justify-end">
            <button
              type="button"
              className="mr-3 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              onClick={() => router.push('/faculty/dashboard/marks')}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={submitting || filteredStudents.length === 0}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-indigo-300"
            >
              {submitting ? 'Uploading...' : 'Upload Marks'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
