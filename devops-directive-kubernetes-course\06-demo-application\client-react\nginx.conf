server {
  listen 8080;
  
  location /ping {
        access_log off;
        add_header 'Content-Type' 'text/plain';
        return 200 "pong";
  }
  location /api/golang/ {
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Server $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;

        rewrite ^/api-golang/(.*) /$1 break;
        proxy_pass http://api-golang:8000/;
  }
  location /api/node/ {
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Server $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;

        rewrite ^/api/node/(.*) /$1 break;
        proxy_pass http://api-node:3000/;
  }
  location / {
    root /usr/share/nginx/html;
    index index.html index.htm;
    try_files $uri $uri/ /index.html =404;
  }
  
  include /etc/nginx/extra-conf.d/*.conf;
}