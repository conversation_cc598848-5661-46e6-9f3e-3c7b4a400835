{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.12.3", "@radix-ui/react-slot": "^1.1.2", "axios": "^1.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.477.0", "next": "15.2.2-canary.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.2.2-canary.0", "@eslint/eslintrc": "^3"}}