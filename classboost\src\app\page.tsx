import Link from 'next/link';

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 text-center">
        <div>
          <h1 className="text-4xl font-extrabold text-gray-900 mb-2">College Portal</h1>
          <p className="text-lg text-gray-600">Welcome to the college management system</p>
        </div>

        <div className="mt-10 space-y-6">
          <div className="bg-white shadow-md rounded-lg p-6 hover:shadow-lg transition-shadow">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Student Portal</h2>
            <p className="text-gray-600 mb-6">Access your academic information, view notifications, check exam results, and request special classes.</p>
            <div className="space-x-4">
              <Link
                href="/login"
                className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
              >
                Login
              </Link>
              <Link
                href="/register"
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-base font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
              >
                Register
              </Link>
            </div>
          </div>

          <div className="bg-white shadow-md rounded-lg p-6 hover:shadow-lg transition-shadow">
            <h2 className="text-2xl font-bold text-indigo-900 mb-4">Faculty Portal</h2>
            <p className="text-gray-600 mb-6">Manage student information, upload marks, and view academic data.</p>
            <div className="space-x-4">
              <Link
                href="/faculty/login"
                className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Login
              </Link>
              <Link
                href="/faculty/register"
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-base font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
              >
                Register
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
