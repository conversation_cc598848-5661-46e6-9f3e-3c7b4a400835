version: "3"

env:
  BORDER: double
  BORDER_FOREGROUND: "212"
  PADDING: "1 1"
  MARGIN: "1 1"
  NAMESPACE: 04--pod

tasks:
  01-create-namespace:
    desc: "Create a namespace for these examples and set as default"
    cmds:
      - kubectl apply -f Namespace.yaml
      - kubens ${NAMESPACE}

  02-wrong-way-create:
    desc: "Create a pod the wrong way"
    cmds:
      - cmd: gum style "🚨 PLEASE DO NOT DO THIS 🚨"
        silent: true
      - kubectl run --image=nginx:1.26.0 -n ${NAMESPACE} created-the-wrong-way

  03-minimal-apply:
    desc: "Apply the minimal pod configuration"
    cmds:
      - kubectl apply -n ${NAMESPACE} -f Pod.nginx-minimal.yaml

  04-minimal-port-forward:
    desc: "Port forward minimal"
    cmds:
      - kubectl port-forward -n ${NAMESPACE} nginx-minimal 8080:80

  05-better-apply:
    desc: "Apply the better pod configuration"
    cmds:
      - kubectl apply -n ${NAMESPACE} -f Pod.nginx-better.yaml

  06-better-port-forward:
    desc: "Port forward to the better pod configuration"
    cmds:
      - kubectl port-forward -n ${NAMESPACE} nginx-better 8080:8080

  07-delete-namespace:
    desc: "Delete the namespace to clean up"
    cmds:
      - cmd: gum style "🚨 Deleting the namespace recursively deletes the resources inside of it! 🚨 "
        silent: true
      - kubectl delete -f Namespace.yaml
