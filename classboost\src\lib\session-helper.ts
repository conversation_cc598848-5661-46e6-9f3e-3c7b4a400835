import { signIn } from 'next-auth/react';

/**
 * Helper function to refresh the session by signing in again
 * This is useful after updating user profile data
 */
export async function refreshSession(email: string, password: string): Promise<boolean> {
  try {
    const result = await signIn('credentials', {
      redirect: false,
      email,
      password,
    });
    
    return !result?.error;
  } catch (error) {
    console.error('Error refreshing session:', error);
    return false;
  }
}
