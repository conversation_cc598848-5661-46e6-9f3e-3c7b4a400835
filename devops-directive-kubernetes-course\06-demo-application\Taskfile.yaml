version: "3"

includes:
  api-golang:
    taskfile: ./api-golang/Taskfile.yaml
    dir: ./api-golang
  api-node:
    taskfile: ./api-node/Taskfile.yaml
    dir: ./api-node
  client-react:
    taskfile: ./client-react/Taskfile.yaml
    dir: ./client-react
  load-generator-python:
    taskfile: ./load-generator-python/Taskfile.yaml
    dir: ./load-generator-python
  postgresql:
    taskfile: ./postgresql/Taskfile.yaml
    dir: ./postgresql

vars:
  DATABASE_URL: "postgres://postgres:foobarbaz@localhost:5432/postgres"

tasks:
  bootstrap-buildx-builder:
    desc: Bootstrap the builder
    cmds:
      - docker buildx create --name mybuilder --driver docker-container --driver-opt network=host --use
      - docker buildx inspect mybuilder --bootstrap
      - docker buildx use mybuilder

  run-local-registry:
    desc: Run a local registry
    cmds:
      - docker run -d -p 5000:5000 registry:2.8
