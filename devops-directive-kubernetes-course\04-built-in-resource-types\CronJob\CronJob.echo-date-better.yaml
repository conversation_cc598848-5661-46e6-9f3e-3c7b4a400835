apiVersion: batch/v1
kind: CronJob
metadata:
  name: echo-date-better
  namespace: 04--cronjob
spec:
  schedule: "* * * * *"
  jobTemplate:
    spec:
      parallelism: 1
      completions: 1
      activeDeadlineSeconds: 100
      backoffLimit: 1
      template:
        metadata:
          labels:
            app: echo-date
        spec:
          containers:
            - name: echo
              image: cgr.dev/chainguard/busybox:latest
              command: ["date"]
              resources:
                limits:
                  memory: "50Mi"
                requests:
                  memory: "50Mi"
                  cpu: "250m"
              securityContext:
                allowPrivilegeEscalation: false
                privileged: false
                runAsUser: 1001
                runAsGroup: 1001
                runAsNonRoot: true
          restartPolicy: Never
          securityContext:
            seccompProfile:
              type: RuntimeDefault
