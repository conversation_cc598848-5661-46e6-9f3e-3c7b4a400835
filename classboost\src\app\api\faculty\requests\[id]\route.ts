import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { getToken } from 'next-auth/jwt';
import connectToDatabase from '@/lib/mongodb';
import SpecialClassRequest from '@/models/SpecialClassRequest';
import { handler as authOptions } from '@/app/api/auth/[...nextauth]/route';
import mongoose from 'mongoose';

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Try both methods to get the session
    let session = await getServerSession(authOptions);
    
    // If session is not available, try to get the token directly
    if (!session || !session.user) {
      const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET || 'your-secret-key' });
      
      if (!token || !token.email || token.role !== 'faculty') {
        console.error('Authentication failed: No valid faculty session or token found');
        return NextResponse.json(
          { message: 'Unauthorized' },
          { status: 401 }
        );
      }
      
      // Create a session-like object from the token
      session = {
        user: {
          email: token.email as string,
          id: token.id as string,
          role: 'faculty',
        }
      };
    } else if (session.user.role !== 'faculty') {
      return NextResponse.json(
        { message: 'Unauthorized: Not a faculty member' },
        { status: 403 }
      );
    }

    // Validate the request ID
    if (!params.id || !mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { message: 'Invalid request ID' },
        { status: 400 }
      );
    }

    const { status } = await req.json();

    // Validate status
    if (!status || !['pending', 'approved', 'rejected'].includes(status)) {
      return NextResponse.json(
        { message: 'Invalid status value' },
        { status: 400 }
      );
    }

    await connectToDatabase();

    // Update request status
    const updatedRequest = await SpecialClassRequest.findByIdAndUpdate(
      params.id,
      { status },
      { new: true }
    );

    if (!updatedRequest) {
      return NextResponse.json(
        { message: 'Special class request not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Request status updated successfully',
      request: updatedRequest,
    });
  } catch (error) {
    console.error('Request status update error:', error);
    return NextResponse.json(
      { message: 'Error updating request status' },
      { status: 500 }
    );
  }
}
