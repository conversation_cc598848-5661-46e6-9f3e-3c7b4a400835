apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: api-node
  namespace: demo-app
spec:
  entryPoints:
    - web
  routes:
    - kind: Rule
      match: Host(`kubernetes-course.devopsdirective.com`) && PathPrefix(`/api/node`)
      middlewares:
        - name: strip-api-prefixes
      services:
        - kind: Service
          name: api-node
          port: 3000
          scheme: http
