import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { getToken } from 'next-auth/jwt';
import connectToDatabase from '@/lib/mongodb';
import Student from '@/models/Student';
import { handler as authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function GET(req: NextRequest) {
  try {
    // Try both methods to get the session
    let session = await getServerSession(authOptions);
    
    // If session is not available, try to get the token directly
    if (!session || !session.user) {
      const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET || 'your-secret-key' });
      
      if (!token || !token.email || token.role !== 'faculty') {
        console.error('Authentication failed: No valid faculty session or token found');
        return NextResponse.json(
          { message: 'Unauthorized' },
          { status: 401 }
        );
      }
      
      // Create a session-like object from the token
      session = {
        user: {
          email: token.email as string,
          id: token.id as string,
          role: 'faculty',
        }
      };
    } else if (session.user.role !== 'faculty') {
      return NextResponse.json(
        { message: 'Unauthorized: Not a faculty member' },
        { status: 403 }
      );
    }

    await connectToDatabase();

    // Get all students with selected fields
    const students = await Student.find({}, {
      fullName: 1,
      email: 1,
      rollNumber: 1,
      department: 1,
      semester: 1,
      profileCompleted: 1,
    }).sort({ department: 1, semester: 1, fullName: 1 });

    return NextResponse.json(students);
  } catch (error) {
    console.error('Students fetch error:', error);
    return NextResponse.json(
      { message: 'Error fetching students' },
      { status: 500 }
    );
  }
}
