# @babel/helper-environment-visitor

> Helper visitor to only visit nodes in the current 'this' context

See our website [@babel/helper-environment-visitor](https://babeljs.io/docs/en/babel-helper-environment-visitor) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/helper-environment-visitor
```

or using yarn:

```sh
yarn add @babel/helper-environment-visitor --dev
```
