import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import connectToDatabase from '@/lib/mongodb';
import ExamResult from '@/models/ExamResult';
import { handler as authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getToken } from 'next-auth/jwt';

export async function GET(req: NextRequest) {
  try {
    // Try both methods to get the session
    let session = await getServerSession(authOptions);

    // If session is not available, try to get the token directly
    if (!session || !session.user) {
      const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET || 'your-secret-key' });

      if (!token || !token.email) {
        console.error('Authentication failed: No valid session or token found');
        return NextResponse.json(
          { message: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Create a session-like object from the token
      session = {
        user: {
          email: token.email as string,
          id: token.id as string,
        }
      };
    }

    await connectToDatabase();

    // Get all exam results for the current student
    const examResults = await ExamResult.find({
      studentEmail: session.user.email
    }).sort({ createdAt: -1 });

    return NextResponse.json(examResults);
  } catch (error) {
    console.error('Exam results fetch error:', error);
    return NextResponse.json(
      { message: 'Error fetching exam results' },
      { status: 500 }
    );
  }
}
