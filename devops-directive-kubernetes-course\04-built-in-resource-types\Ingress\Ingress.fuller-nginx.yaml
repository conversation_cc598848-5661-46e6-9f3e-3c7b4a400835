apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fuller
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
spec:
  ingressClassName: nginx
  defaultBackend:
    service:
      name: default-backend
      port:
        number: 80
  rules:
    - host: "civo-ingress-example.com"
      http:
        paths:
          - path: /foo
            pathType: Prefix
            backend:
              service:
                name: foo-service
                port:
                  number: 80
          - path: /bar
            pathType: ImplementationSpecific
            backend:
              service:
                name: bar-service
                port:
                  number: 8080
          - path: /static/(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: static-service
                port:
                  number: 80
