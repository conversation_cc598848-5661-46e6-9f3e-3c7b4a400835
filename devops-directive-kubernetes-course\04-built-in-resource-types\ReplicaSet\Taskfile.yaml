version: "3"

env:
  BORDER: double
  BORDER_FOREGROUND: "212"
  PADDING: "1 1"
  MARGIN: "1 1"
  NAMESPACE: 04--replicaset

tasks:
  01-create-namespace:
    desc: "Create a namespace for these examples and set as default"
    cmds:
      - kubectl apply -f Namespace.yaml
      - kubens ${NAMESPACE}

  02-minimal-apply:
    desc: "Apply the minimal ReplicaSet configuration"
    cmds:
      - kubectl apply -f ReplicaSet.nginx-minimal.yaml

  03-better-apply:
    desc: "Apply the improved ReplicaSet configuration"
    cmds:
      - kubectl apply -f ReplicaSet.nginx-better.yaml

  04-delete-pods:
    desc: "Delete all the pods! (they will be automatically recreated)"
    cmds:
      - kubectl delete pods --all
      - watch "kubectl get pods"

  05-delete-namespace:
    desc: "Delete the namespace to clean up"
    cmds:
      - cmd: gum style "🚨 Deleting the namespace recursively deletes the resources inside of it! 🚨 "
        silent: true
      - kubectl delete -f Namespace.yaml
