version: "3"

env:
  BORDER: double
  BORDER_FOREGROUND: "212"
  PADDING: "1 1"
  MARGIN: "1 1"
  NAMESPACE: 04--job

tasks:
  01-create-namespace:
    desc: "Create a namespace for these examples and set as default"
    cmds:
      - kubectl apply -f Namespace.yaml
      - kubens ${NAMESPACE}

  02-apply-minimal-pod:
    desc: "Apply the standalone Pod configuration"
    cmds:
      - kubectl apply -f Pod.echo-date-minimal.yaml

  03-apply-minimal-job:
    desc: "Apply the minimal Job configuration"
    cmds:
      - kubectl apply -f Job.echo-date-minimal.yaml

  04-apply-better-job:
    desc: "Apply the better Job configuration"
    cmds:
      - kubectl apply -f Job.echo-date-better.yaml

  05-delete-namespace:
    desc: "Delete the namespace to clean up"
    cmds:
      - cmd: gum style "🚨 Deleting the namespace recursively deletes the resources inside of it! 🚨 "
        silent: true
      - kubectl delete -f Namespace.yaml
