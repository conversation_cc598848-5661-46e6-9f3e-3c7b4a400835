import express from 'express';
import cors from 'cors';

const app = express();
const port = 8080;

app.use(cors());
app.use(express.json());

app.post('/projects', (req, res) => {
  const { prompt } = req.body;
  // For now, just echo back the prompt
  res.json({
    success: true,
    projectId: '123',
    prompt: prompt
  });
});

app.listen(port, () => {
  console.log(`Server running at http://localhost:${port}`);
}); 