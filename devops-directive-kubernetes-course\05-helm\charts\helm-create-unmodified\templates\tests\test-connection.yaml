apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "helm-create-unmodified.fullname" . }}-test-connection"
  labels:
    {{- include "helm-create-unmodified.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "helm-create-unmodified.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
