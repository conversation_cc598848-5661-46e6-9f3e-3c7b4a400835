# Deploying to Multiple Environments

## Examples

### Kustomize (https://github.com/kubernetes-sigs/kustomize)

Kustomize uses an overlay model in which base yaml configurations can be merged with environment specific ones to adapt them to meet that environment's needs.

It is built directly into `kubectl` which makes is a good starting point (no extra tooling required), but is cumbersome and verbose for specific types of patching.

Within the `kustomize` directory there are `base` configurations of each service along with `staging` and `production` overlays which patch the image tag and DNS environment postfix.

### Helm (https://helm.sh/)

We already covered <PERSON><PERSON> in `05-helm`. It is allows for defining "charts" which bundle a number of kubernetes resources and provide a custom interface for deploying.

It is very common, but does require a fair amount of boiler plate which feels heavy for many use cases.

Within the `helm` directory there is a helm chart for the `api-golang` service, with a simple interface that allows for setting the version tag and DNS environment postfix via a `values.yaml` file

### Kluctl (https://kluctl.io/)

Kluctl feels like a hybrid of helm and kustomize, taking the good parts of each and throwing away the bad.

It facilitates templating, provides a diff between what is currently deployed and what will be deployed, and even has a GitOps controller!

Within `kluctl` is a full example (including the 3rd party dependencies) of deploying the application to to a `staging` and `production` environment. This configuration is also used in `14-cicd` for a GitOps deployment.

## Not shown (but worth mentioning)

### Timoni (https://timoni.sh/)

Timoni is similar to these other tools, but uses CUE as its language (which adds type safety and data validation). It also enables more control over the application lifecycle and enables the configuration to be shipped alongside of the application container images as a single OCI artifact.

If you are unfamiliar with CUE, the learning curve can be a bit steep as you learn both the CUE language and the timoni tool.

### CDK8s (https://cdk8s.io/)

cdk8s enables defining kubernetes resources using general purpose programming languages such as TS, Python, Java, and Go. This opens up interesting possibilities around dynamic resource generation, testing, and more that can be difficult with a more limited configuration language like yaml.

### Pulumi (https://www.pulumi.com/kubernetes/)

Similar to cdk8s, Pulumi also has the ability to define kubernetes resources in general purpose programming languages. If you are already using pulumi for managing Infrastructure as Code, it could be useful to also manage your kubernetes deployments there!
