apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: api-golang
  namespace: demo-app
spec:
  entryPoints:
    - web
  routes:
    - kind: Rule
      match: Host(`kubernetes-course.devopsdirective.com`) && PathPrefix(`/api/golang`)
      middlewares:
        - name: strip-api-prefixes
      services:
        - kind: Service
          name: api-golang
          port: 8000
          scheme: http
