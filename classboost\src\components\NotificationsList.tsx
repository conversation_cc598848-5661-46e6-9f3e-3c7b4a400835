'use client';

interface Notification {
  _id: string;
  title: string;
  message: string;
  sender: string;
  createdAt: string;
}

interface NotificationsListProps {
  notifications: Notification[];
}

export default function NotificationsList({ notifications }: NotificationsListProps) {
  if (!notifications || notifications.length === 0) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900">Notifications</h2>
        <p className="mt-2 text-sm text-gray-500">No notifications available.</p>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h2 className="text-lg font-medium text-gray-900">Notifications</h2>
      
      <div className="mt-4 space-y-4">
        {notifications.map((notification) => (
          <div key={notification._id} className="border-b border-gray-200 pb-4 last:border-b-0 last:pb-0">
            <div className="flex justify-between">
              <h3 className="text-sm font-medium text-gray-900">{notification.title}</h3>
              <p className="text-xs text-gray-500">
                {new Date(notification.createdAt).toLocaleDateString()}
              </p>
            </div>
            <p className="mt-1 text-sm text-gray-600">{notification.message}</p>
            <p className="mt-1 text-xs text-gray-500">From: {notification.sender}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
