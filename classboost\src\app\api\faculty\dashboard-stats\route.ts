import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { getToken } from 'next-auth/jwt';
import connectToDatabase from '@/lib/mongodb';
import Student from '@/models/Student';
import ExamResult from '@/models/ExamResult';
import SpecialClassRequest from '@/models/SpecialClassRequest';
import { handler as authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function GET(req: NextRequest) {
  try {
    // Try both methods to get the session
    let session = await getServerSession(authOptions);
    
    // If session is not available, try to get the token directly
    if (!session || !session.user) {
      const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET || 'your-secret-key' });
      
      if (!token || !token.email || token.role !== 'faculty') {
        console.error('Authentication failed: No valid faculty session or token found');
        return NextResponse.json(
          { message: 'Unauthorized' },
          { status: 401 }
        );
      }
      
      // Create a session-like object from the token
      session = {
        user: {
          email: token.email as string,
          id: token.id as string,
          role: 'faculty',
        }
      };
    } else if (session.user.role !== 'faculty') {
      return NextResponse.json(
        { message: 'Unauthorized: Not a faculty member' },
        { status: 403 }
      );
    }

    await connectToDatabase();

    // Get total number of students
    const totalStudents = await Student.countDocuments();
    
    // Get total number of subjects (unique subjects in exam results)
    const subjects = await ExamResult.distinct('subject');
    const totalSubjects = subjects.length;
    
    // Get total number of pending special class requests
    const pendingRequests = await SpecialClassRequest.countDocuments({ status: 'pending' });

    return NextResponse.json({
      totalStudents,
      totalSubjects,
      pendingRequests,
    });
  } catch (error) {
    console.error('Dashboard stats fetch error:', error);
    return NextResponse.json(
      { message: 'Error fetching dashboard statistics' },
      { status: 500 }
    );
  }
}
