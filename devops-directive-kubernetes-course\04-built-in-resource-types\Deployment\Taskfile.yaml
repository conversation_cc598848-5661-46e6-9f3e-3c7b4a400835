version: "3"

env:
  BORDER: double
  BORDER_FOREGROUND: "212"
  PADDING: "1 1"
  MARGIN: "1 1"
  NAMESPACE: 04--deployment

tasks:
  01-create-namespace:
    desc: "Create a namespace for these examples and set as default"
    cmds:
      - kubectl apply -f Namespace.yaml
      - kubens ${NAMESPACE}

  02-apply-minimal:
    desc: "Apply the minimal Deployment configuration"
    cmds:
      - kubectl apply -f Deployment.nginx-minimal.yaml

  03-apply-better:
    desc: "Apply the better Deployment configuration"
    cmds:
      - kubectl apply -f Deployment.nginx-better.yaml

  04-rollout-restart:
    desc: "Roll the pods in one of the deployments"
    cmds:
      - kubectl rollout restart deployment nginx-better
      - watch "kubectl get pods"

  05-delete-namespace:
    desc: "Delete the namespace to clean up"
    cmds:
      - cmd: gum style "🚨 Deleting the namespace recursively deletes the resources inside of it! 🚨 "
        silent: true
      - kubectl delete -f Namespace.yaml
