'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import ProfileInfo from '@/components/ProfileInfo';
import NotificationsList from '@/components/NotificationsList';
import ExamResults from '@/components/ExamResults';
import SpecialClassRequestForm from '@/components/SpecialClassRequestForm';
import RemedialAlert from '@/components/RemedialAlert';

export default function DashboardPage() {
  const { data: session } = useSession();
  const [profile, setProfile] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [examResults, setExamResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [needsRemedial, setNeedsRemedial] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch profile data
        const profileRes = await fetch('/api/profile');
        const profileData = await profileRes.json();
        
        if (!profileRes.ok) {
          throw new Error(profileData.message || 'Failed to fetch profile');
        }
        
        setProfile(profileData);
        
        // Fetch notifications
        const notificationsRes = await fetch('/api/notifications');
        const notificationsData = await notificationsRes.json();
        
        if (!notificationsRes.ok) {
          throw new Error(notificationsData.message || 'Failed to fetch notifications');
        }
        
        setNotifications(notificationsData);
        
        // Fetch exam results
        const examResultsRes = await fetch('/api/exam-results');
        const examResultsData = await examResultsRes.json();
        
        if (!examResultsRes.ok) {
          throw new Error(examResultsData.message || 'Failed to fetch exam results');
        }
        
        setExamResults(examResultsData);
        
        // Check if student needs remedial classes
        const needsRemedial = examResultsData.some((result: any) => result.marks < 15);
        setNeedsRemedial(needsRemedial);
      } catch (error: any) {
        console.error('Dashboard data fetch error:', error);
        setError(error.message || 'An error occurred while fetching data');
      } finally {
        setLoading(false);
      }
    };
    
    if (session?.user) {
      fetchData();
    }
  }, [session]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p>Loading dashboard...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Student Dashboard</h1>
      
      {needsRemedial && <RemedialAlert />}
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <ProfileInfo profile={profile} />
          
          <div className="mt-6">
            <SpecialClassRequestForm />
          </div>
        </div>
        
        <div className="md:col-span-2 space-y-6">
          <NotificationsList notifications={notifications} />
          <ExamResults examResults={examResults} />
        </div>
      </div>
    </div>
  );
}
