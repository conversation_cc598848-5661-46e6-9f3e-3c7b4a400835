import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import connectToDatabase from '@/lib/mongodb';
import Student from '@/models/Student';

export async function POST(req: NextRequest) {
  try {
    const { fullName, email, password } = await req.json();

    // Validate input
    if (!fullName || !email || !password) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    await connectToDatabase();

    // Check if student already exists
    const existingStudent = await Student.findOne({ email });
    if (existingStudent) {
      return NextResponse.json(
        { message: 'Student with this email already exists' },
        { status: 409 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new student
    const student = await Student.create({
      fullName,
      email,
      password: hashedPassword,
      rollNumber: null, // Explicitly set to null to avoid unique constraint issues
      profileCompleted: false,
    });

    return NextResponse.json(
      {
        message: 'Student registered successfully',
        student: {
          id: student._id.toString(),
          fullName: student.fullName,
          email: student.email,
          profileCompleted: student.profileCompleted,
        }
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { message: 'Error registering student' },
      { status: 500 }
    );
  }
}
