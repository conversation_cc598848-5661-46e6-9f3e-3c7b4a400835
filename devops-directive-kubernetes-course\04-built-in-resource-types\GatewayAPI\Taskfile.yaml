version: "3"

env:
  BORDER: double
  BORDER_FOREGROUND: "212"
  PADDING: "1 1"
  MARGIN: "1 1"
  NAMESPACE: 04--gatewayapi

tasks:
  01-create-namespace:
    desc: "Create a namespace for these examples and set as default"
    cmds:
      - kubectl apply -f Namespace.yaml
      - kubens ${NAMESPACE}

  02-apply-deployment:
    desc: "Apply the Deployment configuration"
    cmds:
      - kubectl apply -f Deployment.yaml
      - kubectl apply -f Service.nginx-clusterip.yaml
      - kubectl apply -f Service.nginx-nodeport.yaml

  03-apply-gateway-route-gke:
    desc: "Apply the necessary gateway resources to route traffic to the example deployment using GKE Gateway controller"
    cmds:
      - kubectl apply -f Gateway.gke.yaml
      - kubectl apply -f HTTPRoute.gke.yaml

  04-install-kong-ingress-controller:
    desc: "Install kong ingress controller"
    cmds:
      - cmd: gum style "🚨 We will learn more about helm in the next part of the course... for now just 🤞"
        silent: true
      - |
        helm upgrade --install kong ingress \
          --repo https://charts.konghq.com \
          -n kong \
          --create-namespace \
          --version 0.12.0
      - cmd: gum style "🚨 We will learn more about CustomResourceDefinitions in a later part of the course... for now just 🤞"
        silent: true
      # Kong requires the latest CRDs
      - kubectl apply -f https://github.com/kubernetes-sigs/gateway-api/releases/download/v1.1.0/experimental-install.yaml

  05-apply-gatewayclass-gateway-route:
    desc: "Apply the necessary gateway resources to route traffic to example deployment"
    cmds:
      - kubectl apply -f GatewayClass.kong.yaml
      - kubectl apply -f Gateway.kong.yaml
      - kubectl apply -f HTTPRoute.kong.yaml
      - cmd: |
          gum style \
          "💡 To enable TCPRoutes, some additional configuration is required.

          See: https://docs.konghq.com/kubernetes-ingress-controller/3.1.x/guides/services/tcp/"
        silent: true

  06-delete-namespace:
    desc: "Delete the namespace to clean up"
    cmds:
      - cmd: gum style "🚨 Deleting the namespace recursively deletes the resources inside of it! 🚨 "
        silent: true
      - kubectl delete -f Namespace.yaml
      - kubectl delete ns haproxy-controller
