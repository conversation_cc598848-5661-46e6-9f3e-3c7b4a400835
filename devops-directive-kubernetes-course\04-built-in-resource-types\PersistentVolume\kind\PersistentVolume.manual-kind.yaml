apiVersion: v1
kind: PersistentVolume
metadata:
  name: manual-kind-worker
  labels:
    name: manual-kind
spec:
  accessModes:
    - ReadWriteOnce
  capacity:
    storage: 100Mi
  storageClassName: standard
  local:
    path: /some/path/in/container # Replace with the path to your local storage
  nodeAffinity:
    required:
      nodeSelectorTerms:
        - matchExpressions:
            - key: kubernetes.io/hostname
              operator: In
              values:
                - kind-worker
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: manual-kind-worker2
  labels:
    name: manual-kind
spec:
  accessModes:
    - ReadWriteOnce
  capacity:
    storage: 100Mi
  storageClassName: standard
  local:
    path: /some/path/in/container # Replace with the path to your local storage
  nodeAffinity:
    required:
      nodeSelectorTerms:
        - matchExpressions:
            - key: kubernetes.io/hostname
              operator: In
              values:
                - kind-worker2
