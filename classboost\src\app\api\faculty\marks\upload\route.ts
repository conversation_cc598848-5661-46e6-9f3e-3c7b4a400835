import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { getToken } from 'next-auth/jwt';
import connectToDatabase from '@/lib/mongodb';
import ExamResult from '@/models/ExamResult';
import { handler as authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function POST(req: NextRequest) {
  try {
    // Try both methods to get the session
    let session = await getServerSession(authOptions);
    
    // If session is not available, try to get the token directly
    if (!session || !session.user) {
      const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET || 'your-secret-key' });
      
      if (!token || !token.email || token.role !== 'faculty') {
        console.error('Authentication failed: No valid faculty session or token found');
        return NextResponse.json(
          { message: 'Unauthorized' },
          { status: 401 }
        );
      }
      
      // Create a session-like object from the token
      session = {
        user: {
          email: token.email as string,
          id: token.id as string,
          role: 'faculty',
        }
      };
    } else if (session.user.role !== 'faculty') {
      return NextResponse.json(
        { message: 'Unauthorized: Not a faculty member' },
        { status: 403 }
      );
    }

    const { marks } = await req.json();
    
    if (!marks || !Array.isArray(marks) || marks.length === 0) {
      return NextResponse.json(
        { message: 'No marks data provided' },
        { status: 400 }
      );
    }
    
    await connectToDatabase();
    
    // Process each mark entry
    const results = [];
    
    for (const mark of marks) {
      // Validate mark data
      if (!mark.studentEmail || !mark.subject || !mark.examType || mark.marks === undefined || mark.semester === undefined) {
        return NextResponse.json(
          { message: 'Invalid mark data provided' },
          { status: 400 }
        );
      }
      
      // Check if a result already exists for this student, subject, and exam type
      const existingResult = await ExamResult.findOne({
        studentEmail: mark.studentEmail,
        subject: mark.subject,
        examType: mark.examType,
        semester: mark.semester,
      });
      
      if (existingResult) {
        // Update existing result
        existingResult.marks = mark.marks;
        await existingResult.save();
        results.push(existingResult);
      } else {
        // Create new result
        const newResult = await ExamResult.create({
          studentEmail: mark.studentEmail,
          subject: mark.subject,
          examType: mark.examType,
          marks: mark.marks,
          semester: mark.semester,
        });
        results.push(newResult);
      }
    }
    
    return NextResponse.json({
      message: 'Marks uploaded successfully',
      count: results.length,
    });
  } catch (error) {
    console.error('Marks upload error:', error);
    return NextResponse.json(
      { message: 'Error uploading marks' },
      { status: 500 }
    );
  }
}
