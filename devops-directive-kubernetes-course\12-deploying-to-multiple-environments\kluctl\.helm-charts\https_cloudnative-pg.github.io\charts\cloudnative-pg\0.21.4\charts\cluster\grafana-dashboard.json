{"__inputs": [{"name": "DS_PROMETHEUS", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}, {"name": "DS_EXPRESSION", "label": "Expression", "description": "", "type": "datasource", "pluginId": "__expr__"}], "__elements": {}, "__requires": [{"type": "datasource", "id": "__expr__", "version": "1.0.0"}, {"type": "panel", "id": "alertlist", "name": "Alert list", "version": ""}, {"type": "panel", "id": "bargauge", "name": "Bar gauge", "version": ""}, {"type": "panel", "id": "gauge", "name": "Gauge", "version": ""}, {"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "10.3.3"}, {"type": "panel", "id": "heatmap", "name": "Heatmap", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "table", "name": "Table", "version": ""}, {"type": "panel", "id": "text", "name": "Text", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [{"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": ["cloudnativepg"], "targetBlank": false, "title": "Related Dashboards", "tooltip": "", "type": "dashboards", "url": ""}], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 7, "w": 3, "x": 0, "y": 0}, "id": 676, "options": {"alertInstanceLabelFilter": "{namespace=~\"$namespace\",pod=~\"$instances\"}", "alertName": "", "dashboardAlerts": false, "folder": "", "groupBy": [], "groupMode": "default", "maxItems": 20, "sortOrder": 1, "stateFilter": {"error": true, "firing": true, "noData": false, "normal": true, "pending": true}, "viewMode": "list"}, "title": "<PERSON><PERSON><PERSON>", "type": "alertlist"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 4, "x": 3, "y": 0}, "id": 586, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "markdown"}, "pluginVersion": "10.3.3", "title": "Health", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 12, "x": 7, "y": 0}, "id": 336, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "markdown"}, "pluginVersion": "10.3.3", "title": "Overview", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 2, "x": 19, "y": 0}, "id": 352, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "markdown"}, "pluginVersion": "10.3.3", "title": "Storage", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 3, "x": 21, "y": 0}, "id": 354, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "markdown"}, "pluginVersion": "10.3.3", "title": "Backups", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Cluster Replication Health represents the availability of replica servers available to replace the primary in case of a failure.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "red", "index": 2, "text": "None"}, "1": {"color": "orange", "index": 1, "text": "Degraded"}}, "type": "value"}, {"options": {"from": 2, "result": {"color": "green", "index": 0, "text": "Healthy"}, "to": 999}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 2, "w": 2, "x": 3, "y": 1}, "id": 585, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "max(cnpg_pg_replication_streaming_replicas{namespace=~\"$namespace\", pod=~\"$instances\"} - cnpg_pg_replication_is_wal_receiver_up{namespace=~\"$namespace\", pod=~\"$instances\"})", "legendFormat": "Replication", "range": true, "refId": "A"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "High lag indicates issue with replication. Network or storage interfaces may not have enough bandwidth to handle incoming traffic and replication at the same time.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"color": "text", "index": 0, "text": "No data"}}, "type": "special"}, {"options": {"from": 0, "result": {"color": "green", "index": 1, "text": "Healthy"}, "to": 0.1}, "type": "range"}, {"options": {"from": 0.1, "result": {"color": "yellow", "index": 2, "text": "Sub-second"}, "to": 1}, "type": "range"}, {"options": {"from": 1, "result": {"color": "orange", "index": 3, "text": "Delayed"}, "to": 5}, "type": "range"}, {"options": {"from": 5, "result": {"color": "red", "index": 4, "text": "High"}, "to": **********}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 5, "y": 1}, "id": 590, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value_and_name", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "max(cnpg_pg_replication_lag{namespace=~\"$namespace\",pod=~\"$instances\"}) + max(cnpg_pg_stat_replication_write_lag_seconds{namespace=~\"$namespace\",pod=~\"$instances\"}) + max(cnpg_pg_stat_replication_flush_lag_seconds{namespace=~\"$namespace\",pod=~\"$instances\"}) + max(cnpg_pg_stat_replication_replay_lag_seconds{namespace=~\"$namespace\",pod=~\"$instances\"})", "hide": false, "instant": false, "legendFormat": "Lag", "range": true, "refId": "LAG"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "", "hide": false, "instant": false, "range": true, "refId": "A"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Low disk space or low inode count will result in data loss.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"color": "text", "index": 0, "text": "No data"}}, "type": "special"}, {"options": {"from": 0, "result": {"color": "green", "index": 1, "text": "Healthy"}, "to": 0.8}, "type": "range"}, {"options": {"from": 0.8, "result": {"color": "orange", "index": 2, "text": "Warning"}, "to": 0.9}, "type": "range"}, {"options": {"from": 0.9, "result": {"color": "red", "index": 3, "text": "Critical"}, "to": 0.98}, "type": "range"}, {"options": {"from": 0.98, "result": {"color": "red", "index": 4, "text": "Data Loss"}, "to": 1}, "type": "range"}, {"options": {"from": 1, "result": {"color": "red", "index": 5, "text": "Full"}, "to": **********}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 6, "y": 1}, "id": 613, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value_and_name", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "max((max(max by(persistentvolumeclaim) (1 - kubelet_volume_stats_available_bytes{namespace=\"$namespace\", persistentvolumeclaim=~\"$instances\"} / kubelet_volume_stats_capacity_bytes{namespace=\"$namespace\", persistentvolumeclaim=~\"$instances\"}))) OR (max by(persistentvolumeclaim) (kubelet_volume_stats_inodes_used{namespace=\"$namespace\", persistentvolumeclaim=~\"$instances\"} / kubelet_volume_stats_inodes{namespace=\"$namespace\", persistentvolumeclaim=~\"$instances\"})))", "hide": false, "legendFormat": "Storage", "range": true, "refId": "STORAGE"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-blue", "value": null}]}, "unit": "dateTimeFromNow", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 7, "y": 1}, "id": 338, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "max(cnpg_pg_postmaster_start_time{namespace=~\"$namespace\",pod=~\"$instances\"})*1000", "format": "time_series", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Last failover", "transformations": [], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 6, "w": 2, "x": 9, "y": 1}, "id": 342, "interval": "1m", "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(cnpg_pg_stat_database_xact_commit{namespace=~\"$namespace\",pod=~\"$instances\"}[$__interval])) + sum(rate(cnpg_pg_stat_database_xact_rollback{namespace=~\"$namespace\",pod=~\"$instances\"}[$__interval]))", "interval": "", "legendFormat": "TPS", "range": true, "refId": "TPS"}], "title": "TPS", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "CPU Utilisation from Requests", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"color": "text", "index": 0, "text": "Missing request"}}, "type": "special"}], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 0.8}, {"color": "red", "value": 0.9}]}, "unit": "percentunit", "unitScale": true}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 11, "y": 1}, "id": 344, "interval": "1m", "links": [], "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{namespace=\"$namespace\", pod=~\"$instances\"}) / sum(kube_pod_container_resource_requests{job=\"kube-state-metrics\",  namespace=\"$namespace\", resource=\"cpu\", pod=~\"$instances\"})", "format": "time_series", "instant": true, "intervalFactor": 2, "refId": "A"}], "title": "CPU Utilisation", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Memory Utilisation from Requests", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"color": "text", "index": 0, "text": "Missing request"}}, "type": "special"}], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 0.8}, {"color": "red", "value": 0.9}]}, "unit": "percentunit", "unitScale": true}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 13, "y": 1}, "id": 348, "interval": "1m", "links": [], "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "sum(container_memory_working_set_bytes{job=\"kubelet\", metrics_path=\"/metrics/cadvisor\", namespace=\"$namespace\",container!=\"\", image!=\"\", pod=~\"$instances\"}) / sum(max by(pod) (kube_pod_container_resource_requests{job=\"kube-state-metrics\", namespace=\"$namespace\", resource=\"memory\", pod=~\"$instances\"}))", "format": "time_series", "instant": true, "intervalFactor": 2, "refId": "A"}], "title": "Memory Utilisation", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 30, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "orange", "value": 10}, {"color": "red", "value": 20}]}, "unit": "s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 15, "y": 1}, "id": 465, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "max(cnpg_pg_replication_lag{namespace=~\"$namespace\",pod=~\"$instances\"})", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "Replication Lag", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "orange", "value": 10}, {"color": "red", "value": 20}]}, "unit": "s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 17, "y": 1}, "id": 467, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "max(cnpg_pg_stat_replication_write_lag_seconds{namespace=~\"$namespace\",pod=~\"$instances\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Write Lag", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 0.8}, {"color": "red", "value": 0.9}]}, "unit": "percentunit", "unitScale": true}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 19, "y": 1}, "id": 356, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "max(max by(persistentvolumeclaim) (1 - kubelet_volume_stats_available_bytes{namespace=\"$namespace\", persistentvolumeclaim=~\"$instances\"} / kubelet_volume_stats_capacity_bytes{namespace=\"$namespace\", persistentvolumeclaim=~\"$instances\"}))", "format": "time_series", "instant": true, "interval": "", "legendFormat": "DATA", "range": false, "refId": "DATA"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "max(max by(persistentvolumeclaim) (1 - kubelet_volume_stats_available_bytes{namespace=\"$namespace\", persistentvolumeclaim=~\"(${instances})-wal\"} / kubelet_volume_stats_capacity_bytes{namespace=\"$namespace\", persistentvolumeclaim=~\"(${instances})-wal\"}))", "format": "time_series", "instant": true, "interval": "", "legendFormat": "WAL", "range": false, "refId": "WAL"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "max(\n    sum by (namespace,persistentvolumeclaim) (kubelet_volume_stats_used_bytes{namespace=\"$namespace\", persistentvolumeclaim=~\"(${instances})-tbs.*\"}) \n    /\n    sum by (namespace,persistentvolumeclaim) (kubelet_volume_stats_capacity_bytes{namespace=\"$namespace\", persistentvolumeclaim=~\"(${instances})-tbs.*\"}) \n    *\n    on(namespace, persistentvolumeclaim) group_left(volume)\n    kube_pod_spec_volumes_persistentvolumeclaims_info{pod=~\"$instances\"}\n)", "hide": false, "instant": true, "legendFormat": "Tablespaces (max)", "range": false, "refId": "Max Tablespace"}], "title": "Volume Space Usage", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Elapsed time since the last successful base backup.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"from": 1, "result": {"color": "semi-dark-orange", "index": 0, "text": "Invalid date"}, "to": 1e+42}, "type": "range"}, {"options": {"from": -2147483648, "result": {"color": "red", "index": 1, "text": "N/A"}, "to": -1577847600}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "semi-dark-red", "value": -108000}, {"color": "semi-dark-orange", "value": -107999}, {"color": "#EAB839", "value": -89999}, {"color": "green", "value": -86399}]}, "unit": "dtdurations", "unitScale": true}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 21, "y": 1}, "id": 360, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "-(time() - max(cnpg_collector_last_available_backup_timestamp{namespace=\"$namespace\",pod=~\"$instances\"}))", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "Last Base Backup", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "High resource usage (CPU, Memory, DB Connections)", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"color": "text", "index": 0, "text": "No data"}}, "type": "special"}, {"options": {"from": 0, "result": {"color": "green", "index": 1, "text": "Healthy"}, "to": 0.8}, "type": "range"}, {"options": {"from": 0.8, "result": {"color": "orange", "index": 2, "text": "Warning"}, "to": 0.9}, "type": "range"}, {"options": {"from": 0.9, "result": {"color": "red", "index": 3, "text": "Critical"}, "to": 0.98}, "type": "range"}, {"options": {"from": 0.98, "result": {"color": "red", "index": 4, "text": "Data Loss"}, "to": 999}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 2, "w": 4, "x": 3, "y": 3}, "id": 591, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value_and_name", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "(sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{ namespace=\"$namespace\", pod=~\"$instances\"}) / sum(kube_pod_container_resource_requests{job=\"kube-state-metrics\", namespace=\"$namespace\", resource=\"cpu\", pod=~\"$instances\"}))", "hide": false, "legendFormat": "CPU", "range": true, "refId": "CPU"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "(sum(container_memory_working_set_bytes{job=\"kubelet\", metrics_path=\"/metrics/cadvisor\", namespace=\"$namespace\",container!=\"\", image!=\"\", pod=~\"$instances\"}) / sum(max by(pod) (kube_pod_container_resource_requests{job=\"kube-state-metrics\", namespace=\"$namespace\", resource=\"memory\", pod=~\"$instances\"})))", "hide": false, "instant": false, "legendFormat": "Memory", "range": true, "refId": "MEM"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": " (max(sum by (pod) (cnpg_backends_total{namespace=~\"$namespace\", pod=~\"$instances\"}) / sum by (pod) (cnpg_pg_settings_setting{name=\"max_connections\", namespace=~\"$namespace\", pod=~\"$instances\"})))", "hide": false, "instant": false, "legendFormat": "Connections", "range": true, "refId": "CONNS"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Computes the time since the last known WAL archival in the primary.\nWe ensure to ignore the metric in the replicas by using (1 - cnpg_pg_replication_in_recovery ) as a multiplicative factor. It will be 0 for replicas, 1 for the primary.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"color": "red", "index": 0, "text": "No backups"}}, "type": "special"}, {"options": {"from": -1e+22, "result": {"color": "text", "index": 1, "text": "No data"}, "to": 0}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "dtdurations", "unitScale": true}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 21, "y": 3}, "id": 362, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "max((1 - cnpg_pg_replication_in_recovery{namespace=~\"$namespace\",pod=~\"$instances\"}) * (time() - timestamp(cnpg_pg_stat_archiver_seconds_since_last_archival{namespace=~\"$namespace\",pod=~\"$instances\"}) +\ncnpg_pg_stat_archiver_seconds_since_last_archival{namespace=~\"$namespace\",pod=~\"$instances\"}))", "format": "time_series", "instant": true, "interval": "", "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "Last archived WAL", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-blue", "value": null}]}, "unit": "string", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 7, "y": 4}, "id": 340, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "/^full$/", "values": false}, "showPercentChange": false, "text": {}, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.3.3", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "builder", "exemplar": false, "expr": "cnpg_collector_postgres_version{namespace=~\"$namespace\",pod=~\"$instances\"}", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}}", "range": false, "refId": "A"}], "title": "Version", "transformations": [], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "orange", "value": 10}, {"color": "red", "value": 20}]}, "unit": "s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 15, "y": 4}, "id": 466, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "max(cnpg_pg_stat_replication_flush_lag_seconds{namespace=~\"$namespace\",pod=~\"$instances\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Flush Lag", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "orange", "value": 10}, {"color": "red", "value": 20}]}, "unit": "s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 17, "y": 4}, "id": 468, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "max(cnpg_pg_stat_replication_replay_lag_seconds{namespace=~\"$namespace\",pod=~\"$instances\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "<PERSON><PERSON>", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Base Backups are considered healthy when there has been at least one base backup in the last 24 hours.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"color": "orange", "index": 0, "text": "None"}}, "type": "special"}, {"options": {"from": 0, "result": {"color": "green", "index": 1, "text": "Healthy"}, "to": 90000}, "type": "range"}, {"options": {"from": 90000, "result": {"color": "orange", "index": 2, "text": "Degraded"}, "to": 108000}, "type": "range"}, {"options": {"from": 108000, "result": {"color": "red", "index": 3, "text": "None recent"}, "to": **********}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "WAL"}, "properties": [{"id": "mappings", "value": [{"options": {"match": "null", "result": {"color": "orange", "index": 0, "text": "None"}}, "type": "special"}, {"options": {"from": 0, "result": {"color": "green", "index": 1, "text": "Healthy"}, "to": 360}, "type": "range"}, {"options": {"from": 360, "result": {"color": "orange", "index": 2, "text": "Delayed"}, "to": 900}, "type": "range"}, {"options": {"from": 900, "result": {"color": "red", "index": 3, "text": "Unsynced"}, "to": **********}, "type": "range"}]}]}]}, "gridPos": {"h": 2, "w": 1, "x": 3, "y": 5}, "id": 588, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value_and_name", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "time() - max(cnpg_collector_last_available_backup_timestamp{namespace=\"$namespace\", pod=~\"$instances\"})", "legendFormat": "Backups", "range": true, "refId": "BACKUPS"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Base Backups are considered healthy when there has been at least one base backup in the last 24 hours.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"color": "orange", "index": 0, "text": "None"}}, "type": "special"}, {"options": {"from": 0, "result": {"color": "green", "index": 1, "text": "Healthy"}, "to": 360}, "type": "range"}, {"options": {"from": 360, "result": {"color": "orange", "index": 2, "text": "Delayed"}, "to": 900}, "type": "range"}, {"options": {"from": 900, "result": {"color": "red", "index": 3, "text": "Unsynced"}, "to": **********}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "WAL"}, "properties": [{"id": "mappings", "value": [{"options": {"match": "null", "result": {"color": "orange", "index": 0, "text": "None"}}, "type": "special"}, {"options": {"from": 0, "result": {"color": "green", "index": 1, "text": "Healthy"}, "to": 360}, "type": "range"}, {"options": {"from": 360, "result": {"color": "orange", "index": 2, "text": "Delayed"}, "to": 900}, "type": "range"}, {"options": {"from": 900, "result": {"color": "red", "index": 3, "text": "Unsynced"}, "to": **********}, "type": "range"}]}]}]}, "gridPos": {"h": 2, "w": 1, "x": 4, "y": 5}, "id": 612, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value_and_name", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "max((1 - cnpg_pg_replication_in_recovery{namespace=~\"$namespace\", pod=~\"$instances\"}) * (time() - timestamp(cnpg_pg_stat_archiver_seconds_since_last_archival{namespace=~\"$namespace\", pod=~\"$instances\"}) +\ncnpg_pg_stat_archiver_seconds_since_last_archival{namespace=~\"$namespace\", pod=~\"$instances\"}))", "hide": false, "instant": false, "legendFormat": "WAL", "range": true, "refId": "WAL"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Online if there is at least one ready operator pod", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "red", "index": 0, "text": "Failure"}}, "type": "value"}, {"options": {"from": 1, "result": {"color": "green", "index": 1, "text": "Online"}, "to": 99}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "A"}, "properties": [{"id": "displayName", "value": "Reconcile errors"}]}]}, "gridPos": {"h": 2, "w": 1, "x": 5, "y": 5}, "id": 589, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value_and_name", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "sum by (label_app_kubernetes_io_name) (kube_pod_status_ready{namespace=\"$operatorNamespace\"} * on (pod) group_left( label_app_kubernetes_io_name ) kube_pod_labels{label_app_kubernetes_io_name=~\"cloudnative-pg\"})", "hide": false, "instant": false, "legendFormat": "Operator Status", "range": true, "refId": "A"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "The operator reconcile errors don't distinguish between database cluster or namespaces.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "green", "index": 0, "text": "None"}}, "type": "value"}, {"options": {"from": 1, "result": {"color": "red", "index": 1, "text": "Backup"}, "to": 9}, "type": "range"}, {"options": {"from": 10, "result": {"color": "red", "index": 2, "text": "Cluster"}, "to": 99}, "type": "range"}, {"options": {"from": 100, "result": {"color": "red", "index": 3, "text": "Pooler"}, "to": 999}, "type": "range"}, {"options": {"from": 1000, "result": {"color": "red", "index": 4, "text": "Scheduled Backup"}, "to": 9999}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "A"}, "properties": [{"id": "displayName", "value": "Reconcile errors"}]}]}, "gridPos": {"h": 2, "w": 1, "x": 6, "y": 5}, "id": 655, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value_and_name", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "clamp_max(max(controller_runtime_reconcile_total{namespace=~\"$operatorNamespace\", result=\"error\", controller=\"backup\"}), 1)", "hide": true, "legendFormat": "__auto", "range": true, "refId": "RECONCILE_ERRORS_BACKUP"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "clamp_max(max(controller_runtime_reconcile_total{namespace=~\"$operatorNamespace\", result=\"error\", controller=\"cluster\"}), 1)", "hide": true, "legendFormat": "__auto", "range": true, "refId": "RECONCILE_ERRORS_CLUSTER"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "clamp_max(max(controller_runtime_reconcile_total{namespace=~\"$operatorNamespace\", result=\"error\", controller=\"pooler\"}), 1)", "hide": true, "legendFormat": "__auto", "range": true, "refId": "RECONCILE_ERRORS_POOLER"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "clamp_max(max(controller_runtime_reconcile_total{namespace=~\"$operatorNamespace\", result=\"error\", controller=\"scheduledbackup\"}), 1)", "hide": true, "legendFormat": "__auto", "range": true, "refId": "RECONCILE_ERRORS_SCHEDULED_BACKUP"}, {"datasource": {"type": "__expr__", "uid": "${DS_EXPRESSION}"}, "expression": "$RECONCILE_ERRORS_BACKUP + $RECONCILE_ERRORS_CLUSTER * 10 + $RECONCILE_ERRORS_POOLER * 100 + $RECONCILE_ERRORS_SCHEDULED_BACKUP * 1000", "hide": false, "refId": "A", "type": "math"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 80000000000}, {"color": "red", "value": 90000000000}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 2, "w": 2, "x": 11, "y": 5}, "id": 346, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{namespace=\"$namespace\", pod=~\"$instances\"})", "hide": false, "interval": "", "legendFormat": "Total", "range": true, "refId": "B"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Excluding cache", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 80000000000}, {"color": "red", "value": 90000000000}]}, "unit": "bytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 2, "w": 2, "x": 13, "y": 5}, "id": 350, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "sum(container_memory_working_set_bytes{pod=~\"$instances\", namespace=\"$namespace\", container!=\"\", image!=\"\"})", "hide": false, "interval": "", "legendFormat": "Total", "range": true, "refId": "B"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 60000000000}, {"color": "red", "value": 80000000000}]}, "unit": "decbytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 2, "w": 2, "x": 19, "y": 5}, "id": 358, "links": [], "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "cnpg_pg_database_size_bytes{namespace=\"$namespace\"}", "format": "table", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Database Size", "transformations": [{"id": "groupBy", "options": {"fields": {"Value": {"aggregations": ["max"], "operation": "aggregate"}, "datname": {"aggregations": [], "operation": "groupby"}}}}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "red", "index": 1, "text": "N/A"}}, "type": "value"}, {"options": {"match": "null", "result": {"color": "red", "index": 0, "text": "No backups"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "dateTimeAsIso", "unitScale": true}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 21, "y": 5}, "id": 364, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "max(cnpg_collector_first_recoverability_point{namespace=~\"$namespace\",pod=~\"$instances\"})*1000", "format": "time_series", "instant": true, "interval": "", "legendFormat": "{{pod}}", "range": false, "refId": "A"}], "title": "First Recoverability Point", "type": "stat"}, {"collapsed": false, "datasource": {"uid": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 7}, "id": 12, "panels": [], "targets": [{"datasource": {"uid": "prometheus"}, "refId": "A"}], "title": "Server Health", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 3, "x": 0, "y": 8}, "id": 191, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.3", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Instance", "transparent": true, "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 2, "x": 3, "y": 8}, "id": 192, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.3", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Status", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 3, "x": 5, "y": 8}, "id": 193, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.3", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Clustering / replicas", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 2, "x": 8, "y": 8}, "id": 384, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.3", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Zone", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 4, "x": 10, "y": 8}, "id": 195, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.3", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Connections", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 3, "x": 14, "y": 8}, "id": 196, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.3", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Max Connections", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "gridPos": {"h": 1, "w": 3, "x": 17, "y": 8}, "id": 197, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.3", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Wraparound", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 2, "x": 20, "y": 8}, "id": 313, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.3", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Started", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 2, "x": 22, "y": 8}, "id": 198, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.3", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Version", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 9}, "id": 61, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "<table style=\"width:100%; height:100%;border:0px solid black;\">\n  <td style=\"text-align: center;vertical-align: middle;border:0px solid black; \"><p style=\"font-weight: bold;\">$instances</p>\n  </td>\n</table>", "mode": "html"}, "pluginVersion": "10.3.3", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"index": 0, "text": "Down"}, "1": {"index": 1, "text": "Up"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-red", "value": null}, {"color": "green", "value": 1}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 3, "y": 9}, "id": 33, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.3.3", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "min(kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"})", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "red", "index": 1, "text": "No"}, "1": {"color": "green", "index": 0, "text": "Yes"}}, "type": "value"}], "noValue": "-", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 5, "y": 9}, "id": 60, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.3.3", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "1 - cnpg_pg_replication_in_recovery{namespace=~\"$namespace\",pod=~\"$instances\"} + cnpg_pg_replication_is_wal_receiver_up{namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "transformations": [], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "-", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 1, "x": 7, "y": 9}, "id": 229, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.3.3", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "cnpg_pg_replication_streaming_replicas{namespace=~\"$namespace\", pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "transformations": [], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "This metric depends on exporting the: `topology.kubernetes.io/zone` label through kube-state-metrics (not enabled by default). Can be added by changing its configuration with:\n\n```yaml\nmetricLabelsAllowlist:\n  - nodes=[topology.kubernetes.io/zone]\n```", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 8, "y": 9}, "id": 386, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "/^label_topology_kubernetes_io_zone$/", "values": false}, "showPercentChange": false, "text": {"valueSize": 18}, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.3.3", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "kube_pod_info{namespace=~\"$namespace\", pod=~\"$instances\"} * on(node,instance) group_left(label_topology_kubernetes_io_zone) kube_node_labels", "format": "table", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "transformations": [], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 10, "y": 9}, "id": 58, "options": {"legend": {"calcs": ["last", "mean"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.2.1", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "sum by (pod) (cnpg_backends_total{namespace=~\"$namespace\", pod=~\"$instances\"})", "instant": false, "interval": "", "legendFormat": "-", "refId": "A"}], "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "max": 100, "min": 0, "noValue": "<1%", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 75}, {"color": "red", "value": 90}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 14, "y": 9}, "id": 32, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "horizontal", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto", "text": {}}, "pluginVersion": "10.3.3", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "100 * sum by (pod) (cnpg_backends_total{namespace=~\"$namespace\", pod=~\"$instances\"}) / sum by (pod) (cnpg_pg_settings_setting{name=\"max_connections\", namespace=~\"$namespace\", pod=~\"$instances\"})", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 2147483647, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 200000000}, {"color": "red", "value": 1000000000}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 17, "y": 9}, "id": 8, "options": {"displayMode": "lcd", "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "10.3.3", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "max by (pod) (cnpg_pg_database_xid_age{namespace=~\"$namespace\", pod=~\"$instances\"})", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-blue", "value": null}]}, "unit": "dateTimeFromNow", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 20, "y": 9}, "id": 314, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.3.3", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "cnpg_pg_postmaster_start_time{namespace=~\"$namespace\", pod=~\"$instances\"}*1000", "format": "time_series", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "transformations": [], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-blue", "value": null}]}, "unit": "string", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 22, "y": 9}, "id": 42, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "/^full$/", "values": false}, "showPercentChange": false, "text": {}, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.3.3", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "cnpg_collector_postgres_version{namespace=~\"$namespace\", pod=~\"$instances\"}", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "A"}], "transformations": [], "type": "stat"}, {"collapsed": true, "datasource": {"uid": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 41, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 3, "x": 0, "y": 25}, "id": 187, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.1", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Instance", "transparent": true, "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 3, "x": 3, "y": 25}, "id": 183, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.1", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Max Connections", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 3, "x": 6, "y": 25}, "id": 184, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.1", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Shared Buffers", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 3, "x": 9, "y": 25}, "id": 185, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.1", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Effective <PERSON><PERSON>", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 3, "x": 12, "y": 25}, "id": 186, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.1", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Work Mem", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 3, "x": 15, "y": 25}, "id": 188, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.1", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Maintenance Work Mem", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 3, "x": 18, "y": 25}, "id": 189, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.1", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Random Page Cost", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 3, "x": 21, "y": 25}, "id": 190, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "10.3.1", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Sequential Page Cost", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 26}, "id": 86, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "<table style=\"width:100%; height:100%;border:0px solid black;\">\n  <td style=\"text-align: center;vertical-align: middle;border:0px solid black; \"><p style=\"font-weight: bold;\">$instances</p>\n  </td>\n</table>", "mode": "html"}, "pluginVersion": "10.3.1", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "kube_pod_container_status_ready{container=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-purple"}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 26}, "id": 30, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.3.1", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_pg_settings_setting{name=\"max_connections\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-purple"}]}, "unit": "bytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 26}, "id": 24, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.3.1", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "max by (pod) (cnpg_pg_settings_setting{name=\"shared_buffers\",namespace=~\"$namespace\",pod=~\"$instances\"}) * max by (pod) (cnpg_pg_settings_setting{name=\"block_size\",namespace=~\"$namespace\",pod=~\"$instances\"})", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-purple"}]}, "unit": "bytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 9, "y": 26}, "id": 57, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.3.1", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "max by (pod) (cnpg_pg_settings_setting{name=\"effective_cache_size\",namespace=~\"$namespace\",pod=~\"$instances\"}) * max by (pod) (cnpg_pg_settings_setting{name=\"block_size\",namespace=~\"$namespace\",pod=~\"$instances\"})", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-purple"}]}, "unit": "bytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 12, "y": 26}, "id": 26, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.3.1", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_pg_settings_setting{name=\"work_mem\",namespace=~\"$namespace\",pod=~\"$instances\"} * 1024", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-purple"}]}, "unit": "bytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 15, "y": 26}, "id": 47, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.3.1", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_pg_settings_setting{name=\"maintenance_work_mem\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-purple"}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 18, "y": 26}, "id": 48, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.3.1", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_pg_settings_setting{name=\"random_page_cost\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-purple"}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 26}, "id": 56, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.3.1", "repeat": "instances", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_pg_settings_setting{name=\"seq_page_cost\",namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-purple"}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 32}, "id": 150, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.3.1", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_pg_settings_setting{namespace=~\"$namespace\",pod=~\"$instances\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Configurations", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "container": true, "endpoint": true, "instance": true, "job": true, "name": false, "namespace": true, "pod": false}, "indexByName": {"Time": 0, "Value": 9, "__name__": 1, "container": 2, "endpoint": 3, "instance": 4, "job": 5, "name": 7, "namespace": 8, "pod": 6}, "renameByName": {"__name__": "", "name": "parameter"}}}, {"id": "groupingToMatrix", "options": {"columnField": "pod", "rowField": "parameter", "valueField": "Value"}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"parameter\\pod": "parameter"}}}], "type": "table"}], "targets": [{"datasource": {"uid": "prometheus"}, "refId": "A"}], "title": "Configuration", "type": "row"}, {"collapsed": false, "datasource": {"uid": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 10, "panels": [], "targets": [{"datasource": {"uid": "prometheus"}, "refId": "A"}], "title": "Operational Stats", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"log": 10, "type": "log"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 20}, "id": 273, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "10.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{pod=~\"$instances\", namespace=~\"$namespace\"}) by (pod)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod}}", "refId": "A", "step": 10}], "title": "CPU Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes", "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "quota - requests"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}, {"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineWidth", "value": 2}, {"id": "custom.stacking", "value": {"group": "A", "mode": "none"}}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "quota - limits"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF9830", "mode": "fixed"}}, {"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineWidth", "value": 2}, {"id": "custom.stacking", "value": {"group": "A", "mode": "none"}}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}]}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 20}, "id": 275, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "10.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum(container_memory_working_set_bytes{pod=~\"$instances\", namespace=\"$namespace\", container!=\"\", image!=\"\"}) by (pod)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod}}", "refId": "A", "step": 10}], "title": "Memory Usage (w/o cache)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 27}, "id": 39, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum(cnpg_backends_total{namespace=~\"$namespace\",pod=~\"$instances\"}) by (pod)", "hide": false, "interval": "", "legendFormat": "total ({{pod}})", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum(cnpg_backends_total{namespace=~\"$namespace\",pod=~\"$instances\"}) by (state, pod)", "interval": "", "legendFormat": "{{state}} ({{pod}})", "refId": "A"}], "title": "Session States", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 35}, "id": 50, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(cnpg_pg_stat_database_xact_commit{namespace=~\"$namespace\",pod=~\"$instances\"}[5m])) by (pod)", "interval": "", "legendFormat": "committed ({{pod}})", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum(rate(cnpg_pg_stat_database_xact_rollback{namespace=~\"$namespace\",pod=~\"$instances\"}[5m])) by (pod)", "hide": false, "interval": "", "legendFormat": "rolled back ({{pod}})", "refId": "B"}], "title": "Transactions [5m]", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 35}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "max by (pod) (cnpg_backends_max_tx_duration_seconds{namespace=~\"$namespace\",pod=~\"$instances\"})", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Longest Transaction", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 43}, "id": 55, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "rate(cnpg_pg_stat_database_deadlocks{datname=\"\",namespace=~\"$namespace\",pod=~\"$instances\"}[5m])", "hide": false, "instant": false, "interval": "", "legendFormat": "count ({{pod}})", "refId": "B"}], "title": "Deadlocks [5m]", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 43}, "id": 54, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_backends_waiting_total{namespace=~\"$namespace\",pod=~\"$instances\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Blocked Queries", "type": "timeseries"}, {"collapsed": true, "datasource": {"uid": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 51}, "id": 35, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "#EAB839", "value": 0.7}, {"color": "red", "value": 0.8}]}, "unit": "percentunit", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 52}, "id": 424, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto", "text": {}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "max by(persistentvolumeclaim) (1 - kubelet_volume_stats_available_bytes{namespace=\"$namespace\", persistentvolumeclaim=~\"$instances\"} / kubelet_volume_stats_capacity_bytes{namespace=\"$namespace\", persistentvolumeclaim=~\"$instances\"})", "format": "time_series", "interval": "", "legendFormat": "{{persistentvolumeclaim}}", "range": true, "refId": "FREE_SPACE"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "max by(persistentvolumeclaim) (1 - kubelet_volume_stats_available_bytes{namespace=\"$namespace\", persistentvolumeclaim=~\"(${instances})-wal\"} / kubelet_volume_stats_capacity_bytes{namespace=\"$namespace\", persistentvolumeclaim=~\"(${instances})-wal\"})", "format": "time_series", "interval": "", "legendFormat": "{{persistentvolumeclaim}}", "range": true, "refId": "FREE_SPACE_WAL"}], "title": "Volume Space Usage: PGDATA and WAL", "transformations": [], "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "#EAB839", "value": 0.8}, {"color": "red", "value": 0.9}]}, "unit": "percentunit", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 52}, "id": 426, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "max by(persistentvolumeclaim) (kubelet_volume_stats_inodes_used{namespace=\"$namespace\", persistentvolumeclaim=~\"$instances\"} / kubelet_volume_stats_inodes{namespace=\"$namespace\", persistentvolumeclaim=~\"$instances\"})", "format": "time_series", "interval": "", "legendFormat": "{{persistentvolumeclaim}}", "range": true, "refId": "FREE_INODES"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "max by(persistentvolumeclaim) (kubelet_volume_stats_inodes_used{namespace=\"$namespace\", persistentvolumeclaim=~\"(${instances})-wal\"} / kubelet_volume_stats_inodes{namespace=\"$namespace\", persistentvolumeclaim=~\"(${instances})-wal\"})", "format": "time_series", "interval": "", "legendFormat": "{{persistentvolumeclaim}}", "range": true, "refId": "FREE_INODES_WAL"}], "title": "Volume Inode Usage: PGDATA and WAL", "transformations": [], "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "#EAB839", "value": 0.7}, {"color": "red", "value": 0.8}]}, "unit": "percentunit", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 60}, "id": 564, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto", "text": {}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "sum by (namespace,persistentvolumeclaim) (kubelet_volume_stats_used_bytes{namespace=\"$namespace\", persistentvolumeclaim=~\"(${instances})-tbs.*\"}) \n/\nsum by (namespace,persistentvolumeclaim) (kubelet_volume_stats_capacity_bytes{namespace=\"$namespace\", persistentvolumeclaim=~\"(${instances})-tbs.*\"}) \n*\non(namespace, persistentvolumeclaim) group_left(volume,pod)\nkube_pod_spec_volumes_persistentvolumeclaims_info{pod=~\"$instances\"}", "format": "time_series", "interval": "", "legendFormat": "{{volume}}-{{pod}}", "range": true, "refId": "FREE_SPACE"}], "title": "Volume Space Usage: Tablespaces", "transformations": [], "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 67}, "id": 44, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(cnpg_pg_stat_database_tup_deleted{datname=\"\",namespace=~\"$namespace\",pod=~\"$instances\"}[5m]))", "interval": "", "legendFormat": "deleted", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(cnpg_pg_stat_database_tup_inserted{datname=\"\",namespace=~\"$namespace\",pod=~\"$instances\"}[5m]))", "hide": false, "interval": "", "legendFormat": "inserted", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(cnpg_pg_stat_database_tup_fetched{datname=\"\",namespace=~\"$namespace\",pod=~\"$instances\"}[5m]))", "hide": false, "interval": "", "legendFormat": "fetched", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(cnpg_pg_stat_database_tup_returned{datname=\"\",namespace=~\"$namespace\",pod=~\"$instances\"}[5m]))", "hide": false, "interval": "", "legendFormat": "returned", "range": true, "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(cnpg_pg_stat_database_tup_updated{datname=\"\",namespace=~\"$namespace\",pod=~\"$instances\"}[5m]))", "hide": false, "interval": "", "legendFormat": "updated", "range": true, "refId": "E"}], "title": "Tuple I/O [5m]", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 67}, "id": 46, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "rate(cnpg_pg_stat_database_blks_hit{datname=\"\",namespace=~\"$namespace\",pod=~\"$instances\"}[5m])", "interval": "", "legendFormat": "hit ({{pod}})", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "rate(cnpg_pg_stat_database_blks_read{datname=\"\",namespace=~\"$namespace\",pod=~\"$instances\"}[5m])", "hide": false, "interval": "", "legendFormat": "read ({{pod}})", "range": true, "refId": "B"}], "title": "Block I/O [5m]", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "decbytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 75}, "id": 22, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.0.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "max by (datname) (cnpg_pg_database_size_bytes{datname!~\"template.*\",datname!=\"postgres\",namespace=~\"$namespace\",pod=~\"$instances\"})", "interval": "", "legendFormat": " {{pod}}: {{datname}}", "range": true, "refId": "A"}], "title": "Database Size", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "decbytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 75}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "rate(cnpg_pg_stat_database_temp_bytes{datname=\"\",namespace=~\"$namespace\",pod=~\"$instances\"}[5m])", "instant": false, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "<PERSON>mp Bytes [5m]", "type": "timeseries"}], "targets": [{"datasource": {"uid": "prometheus"}, "refId": "A"}], "title": "Storage & I/O", "type": "row"}, {"collapsed": true, "datasource": {"uid": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 52}, "id": 37, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 53}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_collector_pg_wal_archive_status{value=\"ready\",namespace=~\"$namespace\",pod=~\"$instances\"}", "interval": "", "legendFormat": "ready ({{pod}})", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_collector_pg_wal_archive_status{value=\"done\",namespace=~\"$namespace\",pod=~\"$instances\"}", "hide": false, "interval": "", "legendFormat": "done ({{pod}})", "refId": "B"}], "title": "WAL Segment Archive Status", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 53}, "id": 52, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "rate(cnpg_pg_stat_archiver_archived_count{namespace=~\"$namespace\",pod=~\"$instances\"}[5m])", "interval": "", "legendFormat": "archived ({{pod}})", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "rate(cnpg_pg_stat_archiver_failed_count{namespace=~\"$namespace\",pod=~\"$instances\"}[5m])", "hide": false, "interval": "", "legendFormat": "failed ({{pod}})", "refId": "B"}], "title": "Archiver Status [5m]", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 53}, "id": 53, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_pg_stat_archiver_seconds_since_last_archival{namespace=~\"$namespace\",pod=~\"$instances\"}", "interval": "", "legendFormat": "age ({{pod}})", "refId": "A"}], "title": "Last Archive Age", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 61}, "id": 725, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "cnpg_collector_pg_wal{pod=~\"$instances\", namespace=~\"$namespace\", value=\"count\"}", "instant": false, "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "title": "WAL Count", "type": "timeseries"}], "targets": [{"datasource": {"uid": "prometheus"}, "refId": "A"}], "title": "Write Ahead Log", "type": "row"}, {"collapsed": true, "datasource": {"uid": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 53}, "id": 18, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "#EAB839", "value": 600}, {"color": "dark-red", "value": 3600}]}, "unit": "s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 59}, "id": 16, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_pg_replication_lag{namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": false, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Replication Lag", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 59}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "cnpg_pg_stat_replication_write_lag_seconds{namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": false, "interval": "", "legendFormat": "{{pod}} -> {{application_name}}", "refId": "A"}], "title": "Write Lag", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 59}, "id": 59, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "cnpg_pg_stat_replication_flush_lag_seconds{namespace=~\"$namespace\",pod=~\"$instances\"}", "instant": false, "interval": "", "legendFormat": "{{pod}} -> {{application_name}}", "refId": "A"}], "title": "Flush Lag", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 59}, "id": 20, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "cnpg_pg_stat_replication_replay_lag_seconds{namespace=~\"$namespace\",pod=~\"$instances\"}", "interval": "", "legendFormat": "{{pod}} -> {{application_name}}", "range": true, "refId": "A"}], "title": "<PERSON><PERSON>", "type": "timeseries"}], "targets": [{"datasource": {"uid": "prometheus"}, "refId": "A"}], "title": "Replication", "type": "row"}, {"collapsed": true, "datasource": {"uid": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 54}, "id": 231, "panels": [{"cards": {}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateOranges", "exponent": 0.5, "mode": "spectrum"}, "dataFormat": "timeseries", "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}, "scaleDistribution": {"type": "linear"}}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 55}, "heatmap": {}, "hideZeroBuckets": false, "highlightCards": true, "id": 233, "legend": {"show": false}, "options": {"calculate": true, "calculation": {}, "cellGap": 2, "cellValues": {}, "color": {"exponent": 0.5, "fill": "#b4ff00", "mode": "scheme", "reverse": false, "scale": "exponential", "scheme": "Oranges", "steps": 128}, "exemplars": {"color": "rgba(255,0,255,0.7)"}, "filterValues": {"le": 1e-09}, "legend": {"show": false}, "rowsFrame": {"layout": "auto"}, "showValue": "never", "tooltip": {"mode": "single", "showColorScale": false, "yHistogram": false}, "yAxis": {"axisPlacement": "left", "reverse": false, "unit": "s"}}, "pluginVersion": "10.3.3", "reverseYBuckets": false, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_collector_collection_duration_seconds{namespace=~\"$namespace\",pod=~\"$instances\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Collection Duration", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "yAxis": {"format": "s", "logBase": 1, "show": true}, "yBucketBound": "auto"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 55}, "id": 235, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_collector_last_collection_error{namespace=~\"$namespace\",pod=~\"$instances\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Errors", "type": "timeseries"}], "targets": [{"datasource": {"uid": "prometheus"}, "refId": "A"}], "title": "Collector Stats", "type": "row"}, {"collapsed": true, "datasource": {"uid": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 55}, "id": 239, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "dateTimeAsIso", "unitScale": true}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 56}, "id": 237, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_collector_first_recoverability_point{namespace=~\"$namespace\",pod=~\"$instances\"}*1000 > 0", "format": "time_series", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "First Recoverability Point", "type": "timeseries"}], "targets": [{"datasource": {"uid": "prometheus"}, "refId": "A"}], "title": "Backups", "type": "row"}, {"collapsed": true, "datasource": {"uid": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 56}, "id": 293, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": -1, "drawStyle": "line", "fillOpacity": 8, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 6, "w": 5, "x": 0, "y": 57}, "id": 295, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.2.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_pg_stat_bgwriter_checkpoints_req{namespace=~\"$namespace\",pod=~\"$instances\"}", "format": "time_series", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "req/{{pod}}", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_pg_stat_bgwriter_checkpoints_timed{namespace=~\"$namespace\",pod=~\"$instances\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "timed/{{pod}}", "refId": "A"}], "title": "Requested/Timed", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": -1, "drawStyle": "line", "fillOpacity": 8, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms", "unitScale": true}, "overrides": []}, "gridPos": {"h": 6, "w": 5, "x": 5, "y": 57}, "id": 296, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.2.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_pg_stat_bgwriter_checkpoint_write_time{namespace=~\"$namespace\",pod=~\"$instances\"}", "format": "time_series", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "write/{{pod}}", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "cnpg_pg_stat_bgwriter_checkpoint_sync_time{namespace=~\"$namespace\",pod=~\"$instances\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "sync/{{pod}}", "refId": "A"}], "title": "Write/Sync time", "type": "timeseries"}], "targets": [{"datasource": {"uid": "prometheus"}, "refId": "A"}], "title": "Checkpoints", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 57}, "id": 696, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "red", "index": 0, "text": "No Ready pods"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "A"}, "properties": [{"id": "displayName", "value": "Reconcile errors"}]}]}, "gridPos": {"h": 2, "w": 4, "x": 0, "y": 64}, "id": 697, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value_and_name", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "sum(kube_pod_status_ready{namespace=\"$operatorNamespace\"} * on (pod) group_left( label_app_kubernetes_io_name ) kube_pod_labels{label_app_kubernetes_io_name=~\"cloudnative-pg\"})", "hide": false, "instant": true, "legendFormat": "Ready Operator Pods", "range": false, "refId": "A"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "The operator reconcile errors don't distinguish between database cluster or namespaces.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "green", "index": 0, "text": "None"}}, "type": "value"}, {"options": {"from": 1, "result": {"color": "red", "index": 1}, "to": **********}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "A"}, "properties": [{"id": "displayName", "value": "Reconcile errors"}]}]}, "gridPos": {"h": 2, "w": 4, "x": 4, "y": 64}, "id": 702, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value_and_name", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "max(controller_runtime_reconcile_total{namespace=~\"$operatorNamespace\", result=\"error\", controller=\"cluster\"})", "hide": false, "instant": true, "legendFormat": "Cluster Reconcile Errors", "range": false, "refId": "RECONCILE_ERRORS_BACKUP"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "The operator reconcile errors don't distinguish between database cluster or namespaces.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "green", "index": 0, "text": "None"}}, "type": "value"}, {"options": {"from": 1, "result": {"color": "red", "index": 1}, "to": **********}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "A"}, "properties": [{"id": "displayName", "value": "Reconcile errors"}]}]}, "gridPos": {"h": 2, "w": 4, "x": 8, "y": 64}, "id": 698, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value_and_name", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "max(controller_runtime_reconcile_total{namespace=~\"$operatorNamespace\", result=\"error\", controller=\"backup\"})", "hide": false, "instant": true, "legendFormat": "Backup Reconcile <PERSON>", "range": false, "refId": "RECONCILE_ERRORS_BACKUP"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "The operator reconcile errors don't distinguish between database cluster or namespaces.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "green", "index": 0, "text": "None"}}, "type": "value"}, {"options": {"from": 1, "result": {"color": "red", "index": 1}, "to": **********}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "A"}, "properties": [{"id": "displayName", "value": "Reconcile errors"}]}]}, "gridPos": {"h": 2, "w": 4, "x": 12, "y": 64}, "id": 704, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value_and_name", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "max(controller_runtime_reconcile_total{namespace=~\"$operatorNamespace\", result=\"error\", controller=\"scheduledbackup\"})", "hide": false, "instant": true, "legendFormat": "Scheduled Backup Reconcile Errors", "range": false, "refId": "RECONCILE_ERRORS_BACKUP"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "The operator reconcile errors don't distinguish between database cluster or namespaces.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "green", "index": 0, "text": "None"}}, "type": "value"}, {"options": {"from": 1, "result": {"color": "red", "index": 1}, "to": **********}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "A"}, "properties": [{"id": "displayName", "value": "Reconcile errors"}]}]}, "gridPos": {"h": 2, "w": 4, "x": 16, "y": 64}, "id": 703, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value_and_name", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "max(controller_runtime_reconcile_total{namespace=~\"$operatorNamespace\", result=\"error\", controller=\"pooler\"})", "hide": false, "instant": true, "legendFormat": "<PERSON><PERSON> Recon<PERSON><PERSON>", "range": false, "refId": "RECONCILE_ERRORS_BACKUP"}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"0": {"color": "red", "index": 0, "text": "No Ready pods"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "A"}, "properties": [{"id": "displayName", "value": "Reconcile errors"}]}]}, "gridPos": {"h": 8, "w": 4, "x": 0, "y": 66}, "id": 746, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "sum(kube_pod_status_ready{namespace=\"$operatorNamespace\"} * on (pod) group_left( label_app_kubernetes_io_name ) kube_pod_labels{label_app_kubernetes_io_name=~\"cloudnative-pg\"})", "hide": false, "instant": false, "legendFormat": "Ready Operator Pods", "range": true, "refId": "A"}], "title": "Ready Operator Pods", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "The operator reconcile errors don't distinguish between database cluster or namespaces.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"0": {"color": "green", "index": 0, "text": "None"}}, "type": "value"}, {"options": {"from": 1, "result": {"color": "red", "index": 1}, "to": **********}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "A"}, "properties": [{"id": "displayName", "value": "Reconcile errors"}]}]}, "gridPos": {"h": 8, "w": 4, "x": 4, "y": 66}, "id": 767, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "max(controller_runtime_reconcile_total{namespace=~\"$operatorNamespace\", result=\"error\", controller=\"cluster\"})", "hide": false, "legendFormat": "Cluster Reconcile Errors", "range": true, "refId": "RECONCILE_ERRORS_BACKUP"}], "title": "Cluster Reconcile Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "The operator reconcile errors don't distinguish between database cluster or namespaces.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"0": {"color": "green", "index": 0, "text": "None"}}, "type": "value"}, {"options": {"from": 1, "result": {"color": "red", "index": 1}, "to": **********}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "A"}, "properties": [{"id": "displayName", "value": "Reconcile errors"}]}]}, "gridPos": {"h": 8, "w": 4, "x": 8, "y": 66}, "id": 768, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "max(controller_runtime_reconcile_total{namespace=~\"$operatorNamespace\", result=\"error\", controller=\"backup\"})", "hide": false, "legendFormat": "Backup Reconcile <PERSON>", "range": true, "refId": "RECONCILE_ERRORS_BACKUP"}], "title": "Backup Reconcile <PERSON>", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "The operator reconcile errors don't distinguish between database cluster or namespaces.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"0": {"color": "green", "index": 0, "text": "None"}}, "type": "value"}, {"options": {"from": 1, "result": {"color": "red", "index": 1}, "to": **********}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "A"}, "properties": [{"id": "displayName", "value": "Reconcile errors"}]}]}, "gridPos": {"h": 8, "w": 4, "x": 12, "y": 66}, "id": 790, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "max(controller_runtime_reconcile_total{namespace=~\"$operatorNamespace\", result=\"error\", controller=\"scheduledbackup\"})", "hide": false, "instant": false, "legendFormat": "Scheduled Backup Reconcile Errors", "range": true, "refId": "RECONCILE_ERRORS_BACKUP"}], "title": "Scheduled Backup Reconcile Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "The operator reconcile errors don't distinguish between database cluster or namespaces.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"0": {"color": "green", "index": 0, "text": "None"}}, "type": "value"}, {"options": {"from": 1, "result": {"color": "red", "index": 1}, "to": **********}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "A"}, "properties": [{"id": "displayName", "value": "Reconcile errors"}]}]}, "gridPos": {"h": 8, "w": 4, "x": 16, "y": 66}, "id": 769, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "max(controller_runtime_reconcile_total{namespace=~\"$operatorNamespace\", result=\"error\", controller=\"pooler\"})", "hide": false, "legendFormat": "<PERSON><PERSON> Recon<PERSON><PERSON>", "range": true, "refId": "RECONCILE_ERRORS_BACKUP"}], "title": "<PERSON><PERSON> Recon<PERSON><PERSON>", "type": "timeseries"}], "title": "Operator", "type": "row"}], "refresh": "30s", "revision": 1, "schemaVersion": 39, "tags": ["cloudnativepg"], "templating": {"list": [{"current": {"selected": false, "text": "Prometheus", "value": "prometheus"}, "hide": 0, "includeAll": false, "label": "Datasource", "multi": false, "name": "DS_PROMETHEUS", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "label_values(controller_runtime_active_workers,namespace)", "description": "Namespace where the CNPG operator is located", "hide": 0, "includeAll": false, "label": "Operator Namespace", "multi": false, "name": "operatorNamespace", "options": [], "query": {"qryType": 1, "query": "label_values(controller_runtime_active_workers,namespace)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "cnpg_collector_up", "description": "Namespace where the database cluster is located", "hide": 0, "includeAll": false, "label": "Database Namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "cnpg_collector_up", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "/namespace=\"(?<text>[^\"]+)/g", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "cnpg_collector_up{namespace=~\"$namespace\"}", "description": "CNPG Cluster", "hide": 0, "includeAll": false, "label": "Cluster", "multi": false, "name": "cluster", "options": [], "query": {"query": "cnpg_collector_up{namespace=~\"$namespace\"}", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 2, "regex": "/\\bcluster\\b=\"(?<text>[^\"]+)/g", "skipUrlSync": false, "sort": 1, "type": "query"}, {"allValue": "$cluster-([1-9][0-9]*)$", "current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "cnpg_collector_up{namespace=~\"$namespace\",pod=~\"$cluster-([1-9][0-9]*)$\"}", "description": "Database cluster instances", "hide": 0, "includeAll": true, "label": "Instances", "multi": true, "name": "instances", "options": [], "query": {"qryType": 4, "query": "cnpg_collector_up{namespace=~\"$namespace\",pod=~\"$cluster-([1-9][0-9]*)$\"}", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 2, "regex": "/pod=\"(?<text>[^\"]+)/g", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-7d", "to": "now"}, "timepicker": {"nowDelay": ""}, "timezone": "", "title": "CloudNativePG", "uid": "cloudnative-pg", "version": 2, "weekStart": ""}