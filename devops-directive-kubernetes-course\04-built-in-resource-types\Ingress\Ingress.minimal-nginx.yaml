apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: minimal-nginx
  # Can use
  # annotations:
  #   kubernetes.io/ingress.class: "nginx"
spec:
  ingressClassName: nginx
  rules:
    - host: "ingress-example-nginx.com"
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: nginx-clusterip
                port:
                  number: 80
