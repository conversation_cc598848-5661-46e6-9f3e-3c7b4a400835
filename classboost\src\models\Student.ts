import mongoose from 'mongoose';

const StudentSchema = new mongoose.Schema({
  fullName: {
    type: String,
    required: [true, 'Please provide your full name'],
    maxlength: [60, 'Name cannot be more than 60 characters'],
  },
  rollNumber: {
    type: String,
    unique: true,
    sparse: true, // This allows null values and only enforces uniqueness on non-null values
  },
  email: {
    type: String,
    required: [true, 'Please provide your email'],
    unique: true,
    match: [/^\S+@\S+\.\S+$/, 'Please provide a valid email'],
  },
  password: {
    type: String,
    required: [true, 'Please provide a password'],
    minlength: [6, 'Password should be at least 6 characters'],
  },
  department: {
    type: String,
    required: false,
  },
  semester: {
    type: Number,
    required: false,
  },
  profileCompleted: {
    type: Boolean,
    default: false,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

export default mongoose.models.Student || mongoose.model('Student', StudentSchema);
