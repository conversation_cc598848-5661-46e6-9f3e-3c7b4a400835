'use client';

interface ProfileProps {
  profile: {
    fullName: string;
    email: string;
    rollNumber?: string;
    department: string;
    semester: number;
  } | null;
}

export default function ProfileInfo({ profile }: ProfileProps) {
  if (!profile) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900">Profile Information</h2>
        <p className="mt-2 text-sm text-gray-500">Loading profile...</p>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h2 className="text-lg font-medium text-gray-900">Profile Information</h2>

      <div className="mt-4 space-y-3">
        <div>
          <p className="text-sm font-medium text-gray-500">Full Name</p>
          <p className="mt-1 text-sm text-gray-900">{profile.fullName}</p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">Email</p>
          <p className="mt-1 text-sm text-gray-900">{profile.email}</p>
        </div>

        {profile.rollNumber && (
          <div>
            <p className="text-sm font-medium text-gray-500">Roll Number</p>
            <p className="mt-1 text-sm text-gray-900">{profile.rollNumber}</p>
          </div>
        )}

        <div>
          <p className="text-sm font-medium text-gray-500">Department</p>
          <p className="mt-1 text-sm text-gray-900">{profile.department}</p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">Semester</p>
          <p className="mt-1 text-sm text-gray-900">{profile.semester}</p>
        </div>
      </div>
    </div>
  );
}
