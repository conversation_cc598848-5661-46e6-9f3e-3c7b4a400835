{"name": "bolt-mobile-app", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types"}, "devDependencies": {"prettier": "^3.5.0", "turbo": "^2.4.4", "typescript": "5.7.3"}, "engines": {"node": ">=18"}, "packageManager": "bun@1.2.4", "workspaces": ["apps/*", "packages/*"]}