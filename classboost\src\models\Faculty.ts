import mongoose from 'mongoose';

const FacultySchema = new mongoose.Schema({
  fullName: {
    type: String,
    required: [true, 'Please provide your full name'],
    maxlength: [60, 'Name cannot be more than 60 characters'],
  },
  email: {
    type: String,
    required: [true, 'Please provide your email'],
    unique: true,
    match: [/^\S+@\S+\.\S+$/, 'Please provide a valid email'],
  },
  password: {
    type: String,
    required: [true, 'Please provide a password'],
    minlength: [6, 'Password should be at least 6 characters'],
  },
  department: {
    type: String,
    required: [true, 'Please provide your department'],
  },
  subjects: {
    type: [String],
    default: [],
  },
  role: {
    type: String,
    default: 'faculty',
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

export default mongoose.models.Faculty || mongoose.model('Faculty', FacultySchema);
