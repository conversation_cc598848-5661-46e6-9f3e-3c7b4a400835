import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { getToken } from 'next-auth/jwt';
import connectToDatabase from '@/lib/mongodb';
import ExamResult from '@/models/ExamResult';
import Student from '@/models/Student';
import { handler as authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function GET(req: NextRequest) {
  try {
    // Try both methods to get the session
    let session = await getServerSession(authOptions);
    
    // If session is not available, try to get the token directly
    if (!session || !session.user) {
      const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET || 'your-secret-key' });
      
      if (!token || !token.email || token.role !== 'faculty') {
        console.error('Authentication failed: No valid faculty session or token found');
        return NextResponse.json(
          { message: 'Unauthorized' },
          { status: 401 }
        );
      }
      
      // Create a session-like object from the token
      session = {
        user: {
          email: token.email as string,
          id: token.id as string,
          role: 'faculty',
        }
      };
    } else if (session.user.role !== 'faculty') {
      return NextResponse.json(
        { message: 'Unauthorized: Not a faculty member' },
        { status: 403 }
      );
    }

    await connectToDatabase();

    // Get all exam results
    const examResults = await ExamResult.find({})
      .sort({ createdAt: -1 });
    
    // Get all students to add names to the results
    const students = await Student.find({}, {
      email: 1,
      fullName: 1,
      rollNumber: 1,
    });
    
    // Create a map of student emails to names and roll numbers
    const studentMap = new Map();
    students.forEach(student => {
      studentMap.set(student.email, {
        name: student.fullName,
        rollNumber: student.rollNumber,
      });
    });
    
    // Add student names and roll numbers to exam results
    const resultsWithStudentInfo = examResults.map(result => {
      const studentInfo = studentMap.get(result.studentEmail);
      return {
        ...result.toObject(),
        studentName: studentInfo ? studentInfo.name : undefined,
        rollNumber: studentInfo ? studentInfo.rollNumber : undefined,
      };
    });

    return NextResponse.json(resultsWithStudentInfo);
  } catch (error) {
    console.error('Exam results fetch error:', error);
    return NextResponse.json(
      { message: 'Error fetching exam results' },
      { status: 500 }
    );
  }
}
