apiVersion: v1
kind: Service
metadata:
  name: {{ include "helm-create-unmodified.fullname" . }}
  labels:
    {{- include "helm-create-unmodified.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "helm-create-unmodified.selectorLabels" . | nindent 4 }}
