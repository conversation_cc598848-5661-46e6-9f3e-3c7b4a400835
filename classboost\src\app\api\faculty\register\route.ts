import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import connectToDatabase from '@/lib/mongodb';
import Faculty from '@/models/Faculty';

export async function POST(req: NextRequest) {
  try {
    const { fullName, email, password, department, subjects, registrationCode } = await req.json();

    // Validate input
    if (!fullName || !email || !password || !department || !registrationCode) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate registration code
    if (registrationCode !== 'FACULTY2023') { // Simple hardcoded code for demonstration
      return NextResponse.json(
        { message: 'Invalid registration code' },
        { status: 403 }
      );
    }

    await connectToDatabase();

    // Check if faculty already exists
    const existingFaculty = await Faculty.findOne({ email });
    if (existingFaculty) {
      return NextResponse.json(
        { message: 'Faculty with this email already exists' },
        { status: 409 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new faculty
    const faculty = await Faculty.create({
      fullName,
      email,
      password: hashedPassword,
      department,
      subjects: subjects || [],
      role: 'faculty',
    });

    return NextResponse.json(
      {
        message: 'Faculty registered successfully',
        faculty: {
          id: faculty._id.toString(),
          fullName: faculty.fullName,
          email: faculty.email,
          department: faculty.department,
        }
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { message: 'Error registering faculty' },
      { status: 500 }
    );
  }
}
