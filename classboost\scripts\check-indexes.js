const mongoose = require('mongoose');

// MongoDB connection
const MONGODB_URI = "mongodb+srv://infernogane:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";

async function checkIndexes() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get the Student collection
    const db = mongoose.connection.db;
    const collections = await db.listCollections().toArray();
    console.log('Collections:', collections.map(c => c.name));

    // Check if students collection exists
    if (collections.some(c => c.name === 'students')) {
      // Get indexes on the students collection
      const indexes = await db.collection('students').indexes();
      console.log('Indexes on students collection:', indexes);
    } else {
      console.log('Students collection does not exist yet');
    }

  } catch (error) {
    console.error('Error checking indexes:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
}

// Run the function
checkIndexes();
