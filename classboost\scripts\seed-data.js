const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// MongoDB connection
const MONGODB_URI = "mongodb+srv://infernogane:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";

// Define schemas
const StudentSchema = new mongoose.Schema({
  fullName: String,
  email: String,
  password: String,
  rollNumber: String,
  department: String,
  semester: Number,
  profileCompleted: Boolean,
  createdAt: { type: Date, default: Date.now },
});

const NotificationSchema = new mongoose.Schema({
  title: String,
  message: String,
  sender: String,
  createdAt: { type: Date, default: Date.now },
});

const ExamResultSchema = new mongoose.Schema({
  studentEmail: String,
  subject: String,
  examType: String,
  marks: Number,
  semester: Number,
  createdAt: { type: Date, default: Date.now },
});

// Create models
const Student = mongoose.model('Student', StudentSchema);
const Notification = mongoose.model('Notification', NotificationSchema);
const ExamResult = mongoose.model('ExamResult', ExamResultSchema);

// Sample data
const sampleStudents = [
  {
    fullName: 'John Doe',
    email: '<EMAIL>',
    password: 'password123',
    rollNumber: 'CS2023001',
    department: 'Computer Science',
    semester: 3,
    profileCompleted: true,
  },
  {
    fullName: 'Jane Smith',
    email: '<EMAIL>',
    password: 'password123',
    rollNumber: 'IT2023002',
    department: 'Information Technology',
    semester: 4,
    profileCompleted: true,
  },
];

const sampleNotifications = [
  {
    title: 'Welcome to the new semester',
    message: 'We hope you have a great learning experience this semester. Please check your timetable for class schedules.',
    sender: 'Admin',
  },
  {
    title: 'Upcoming Exams',
    message: 'Mid-semester exams will begin next week. Please prepare accordingly and check the exam schedule.',
    sender: 'Examination Department',
  },
  {
    title: 'Library Notice',
    message: 'The library will be closed for maintenance this weekend. Please return any borrowed books before Friday.',
    sender: 'Library',
  },
];

const sampleExamResults = [
  {
    studentEmail: '<EMAIL>',
    subject: 'Mathematics',
    examType: 'CAT 1',
    marks: 18,
    semester: 3,
  },
  {
    studentEmail: '<EMAIL>',
    subject: 'Physics',
    examType: 'CAT 1',
    marks: 12,
    semester: 3,
  },
  {
    studentEmail: '<EMAIL>',
    subject: 'Computer Science',
    examType: 'CAT 1',
    marks: 22,
    semester: 3,
  },
  {
    studentEmail: '<EMAIL>',
    subject: 'Mathematics',
    examType: 'CAT 1',
    marks: 20,
    semester: 4,
  },
  {
    studentEmail: '<EMAIL>',
    subject: 'Information Technology',
    examType: 'CAT 1',
    marks: 25,
    semester: 4,
  },
];

// Seed function
async function seedDatabase() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing data
    await Student.deleteMany({});
    await Notification.deleteMany({});
    await ExamResult.deleteMany({});
    console.log('Cleared existing data');

    // Hash passwords for students
    const hashedStudents = await Promise.all(
      sampleStudents.map(async (student) => {
        const hashedPassword = await bcrypt.hash(student.password, 10);
        return { ...student, password: hashedPassword };
      })
    );

    // Insert sample data
    await Student.insertMany(hashedStudents);
    await Notification.insertMany(sampleNotifications);
    await ExamResult.insertMany(sampleExamResults);

    console.log('Sample data inserted successfully');
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
}

// Run the seed function
seedDatabase();
