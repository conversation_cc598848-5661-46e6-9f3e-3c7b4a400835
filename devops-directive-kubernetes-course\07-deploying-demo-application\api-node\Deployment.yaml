apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-node
  namespace: demo-app
  labels:
    app: api-node
spec:
  replicas: 1
  selector:
    matchLabels:
      app: api-node
  template:
    metadata:
      labels:
        app: api-node
    spec:
      containers:
        - name: api-node
          image: sidpalas/devops-directive-docker-course-api-node:foobarbaz
          env:
            - name: PORT
              value: "3000"
          envFrom:
            - secretRef:
                name: api-node-database-url
          ports:
            - containerPort: 3000
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /ping
              port: 3000
          resources:
            limits:
              memory: "100Mi"
            requests:
              memory: "100Mi"
              cpu: "50m"
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
      securityContext:
        seccompProfile:
          type: RuntimeDefault
