apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: individual-pvcs-civo
spec:
  serviceName: "nginx"
  replicas: 2
  selector:
    matchLabels:
      app: nginx-sts-civo
  template:
    metadata:
      labels:
        app: nginx-sts-civo
    spec:
      containers:
        - name: nginx
          image: nginx:1.26.0
          ports:
            - containerPort: 80
          volumeMounts:
            - name: data
              mountPath: /some/mount/path
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 1Gi
        storageClassName: civo-volume
---
apiVersion: v1
kind: Service
metadata:
  name: individual-pvcss
spec:
  clusterIP: None
  selector:
    app: nginx-sts-civo
