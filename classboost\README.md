# College Management System

A comprehensive web application for college management with separate portals for students and faculty. Students can manage their academic information, view notifications, check exam results, and request special classes. Faculty can manage student information, upload marks, and view academic data.

## Features

### Student Portal
- **User Authentication**: Secure login and registration system for students
- **Profile Management**: Students can complete their profile with personal and academic information
- **Dashboard**: Centralized view of student information
- **Notifications**: View important announcements from administrators and teachers
- **Exam Results**: Check marks for various exams (CAT 1, CAT 2, etc.)
- **Remedial Class Alerts**: Automatic alerts for students with marks below 15
- **Special Class Requests**: Submit requests for special classes with reasons

### Faculty Portal
- **Faculty Authentication**: Secure login and registration system for faculty members
- **Dashboard**: Overview of academic statistics
- **Student Management**: View and search student details
- **Marks Management**: Upload and edit student marks
- **Performance Analysis**: View student performance statistics
- **Profile Management**: Update faculty profile information
- **Special Class Requests**: Manage student requests for special classes

## Technologies Used

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: MongoDB Atlas
- **Authentication**: NextAuth.js

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn
- MongoDB Atlas account

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Run the development server:

```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) with your browser to see the application

### Seeding the Database

To populate the database with sample data, run:

```bash
node scripts/seed-data.js
node scripts/seed-faculty.js
```

This will create sample students, faculty, notifications, and exam results for testing.

### Sample Login Credentials

#### Student Portal

- Email: <EMAIL>
- Password: password123

Or

- Email: <EMAIL>
- Password: password123

#### Faculty Portal

- Email: <EMAIL>
- Password: password123

Or

- Email: <EMAIL>
- Password: password123

#### Faculty Registration Code

To register as a faculty member, use the following registration code:

- Registration Code: FACULTY2023

## Project Structure

- `src/app`: Next.js application pages and API routes
- `src/components`: Reusable React components
- `src/lib`: Utility functions and database connection
- `src/models`: MongoDB models
- `scripts`: Database seeding scripts
