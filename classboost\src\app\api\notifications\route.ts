import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import connectToDatabase from '@/lib/mongodb';
import Notification from '@/models/Notification';
import { handler as authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getToken } from 'next-auth/jwt';

export async function GET(req: NextRequest) {
  try {
    // Try both methods to get the session
    let session = await getServerSession(authOptions);

    // If session is not available, try to get the token directly
    if (!session || !session.user) {
      const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET || 'your-secret-key' });

      if (!token || !token.email) {
        console.error('Authentication failed: No valid session or token found');
        return NextResponse.json(
          { message: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Create a session-like object from the token
      session = {
        user: {
          email: token.email as string,
          id: token.id as string,
        }
      };
    }

    await connectToDatabase();

    // Get all notifications, sorted by creation date (newest first)
    const notifications = await Notification.find({})
      .sort({ createdAt: -1 })
      .limit(10);

    return NextResponse.json(notifications);
  } catch (error) {
    console.error('Notifications fetch error:', error);
    return NextResponse.json(
      { message: 'Error fetching notifications' },
      { status: 500 }
    );
  }
}
