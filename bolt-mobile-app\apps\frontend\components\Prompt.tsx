"use client";

import { Send } from "lucide-react";
import { Textarea } from "./ui/textarea";
import { Button } from "./ui/button";
import { useState } from "react";
import axios from "axios";
import { useAuth } from "@clerk/nextjs";

/*
export function Prompt() {
  const [prompt, setPrompt] = useState("");
  const {getToken} = useAuth();

  return (
    <div>
      <Textarea 
        placeholder="Create a chess application..." value={prompt} onChange={(e) => setPrompt(e.target.value)}
      />  
      <div className="flex justify-end pt-2">
        <Button onClick={async ()=>{

          const token =await getToken();
          const response = await axios.post("{BACKEND_URL}/projects", {
            prompt: prompt, 
          },{
            headers:{
              "Authorization": `Bearer ${token}` 
            }
          }) 
          console.log(response.data);
        }}>
          <Send />
        </Button>
      </div>
    </div>
  );
}
*/
const BACKEND_URL ="http://localhost:8080";

export function Prompt() {
  const [prompt, setPrompt] = useState("");
  const {getToken} = useAuth();

  return (
    <div>
      <Textarea 
        placeholder="Create a chess application..." value={prompt} onChange={(e) => setPrompt(e.target.value)}
      />  
      <div className="flex justify-end pt-2">
        <Button onClick={async ()=>{
          const token = await getToken();
          const response = await axios.post(`http://localhost:8080/projects`, {
            prompt: prompt,
          },{
            headers:{
              "Authorization": `Bearer ${token}`
            }
          });
          console.log(response.data);
        }}>
          <Send />
        </Button>
      </div>
    </div>
  );
}
