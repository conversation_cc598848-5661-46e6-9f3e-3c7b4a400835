import mongoose from 'mongoose';

const ExamResultSchema = new mongoose.Schema({
  studentEmail: {
    type: String,
    required: [true, 'Student email is required'],
    ref: 'Student',
  },
  subject: {
    type: String,
    required: [true, 'Subject name is required'],
  },
  examType: {
    type: String,
    required: [true, 'Exam type is required'],
    enum: ['CAT 1', 'CAT 2', 'Final'],
  },
  marks: {
    type: Number,
    required: [true, 'Marks are required'],
    min: [0, 'Marks cannot be negative'],
    max: [100, 'Marks cannot exceed 100'],
  },
  semester: {
    type: Number,
    required: [true, 'Semester is required'],
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

export default mongoose.models.ExamResult || mongoose.model('ExamResult', ExamResultSchema);
