version: "3"

env:
  BORDER: double
  BORDER_FOREGROUND: "212"
  PADDING: "1 1"
  MARGIN: "1 1"
  NAMESPACE: 04--configmap

tasks:
  01-create-namespace:
    desc: "Create a namespace for these examples and set as default"
    cmds:
      - kubectl apply -f Namespace.yaml
      - kubens ${NAMESPACE}

  02-apply-file-like:
    desc: "Apply the ConfigMap with file like keys"
    cmds:
      - kubectl apply -f ConfigMap.file-like-keys.yaml

  03-apply-property-like:
    desc: "Apply the ConfigMap with property like keys"
    cmds:
      - kubectl apply -f ConfigMap.property-like-keys.yaml

  04-apply-pod:
    desc: "Apply the ConfigMap with property like keys"
    cmds:
      - kubectl apply -f Pod.configmap-example.yaml

  05-exec-cat-config:
    desc: "Cat /etc/config/conf.yaml within the container"
    cmds:
      - kubectl exec configmap-example -c nginx -- cat /etc/config/conf.yml

  06-exec-printenv:
    desc: "Print the environment variables within the container"
    cmds:
      - kubectl exec configmap-example -c nginx -- printenv

  07-delete-namespace:
    desc: "Delete the namespace to clean up"
    cmds:
      - cmd: gum style "🚨 Deleting the namespace recursively deletes the resources inside of it! 🚨 "
        silent: true
      - kubectl delete -f Namespace.yaml
