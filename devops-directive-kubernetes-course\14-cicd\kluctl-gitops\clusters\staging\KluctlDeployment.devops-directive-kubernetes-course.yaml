apiVersion: gitops.kluctl.io/v1beta1
kind: KluctlDeployment
metadata:
  name: demo-app
  namespace: kluctl-gitops
spec:
  interval: 5m
  source:
    git:
      url: https://github.com/sidpalas/devops-directive-kubernetes-course.git
      path: 12-deploying-to-multiple-environments/kluctl
  target: staging
  context: default
  # let it automatically clean up orphan KluctlDeployment resources
  prune: true
  delete: true
