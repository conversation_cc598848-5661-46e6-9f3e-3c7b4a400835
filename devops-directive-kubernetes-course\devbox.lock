{"lockfile_version": "1", "packages": {"act@latest": {"last_modified": "2024-06-03T07:19:07Z", "resolved": "github:NixOS/nixpkgs/4a4ecb0ab415c9fccfb005567a215e6a9564cdf5#act", "source": "devbox-search", "version": "0.2.63", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/fhn4kf6qa88l2pl0n202b4mplyc5pcyf-act-0.2.63", "default": true}], "store_path": "/nix/store/fhn4kf6qa88l2pl0n202b4mplyc5pcyf-act-0.2.63"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/5pmgbyh36x428ivx8dg2rq3rvaicjb66-act-0.2.63", "default": true}], "store_path": "/nix/store/5pmgbyh36x428ivx8dg2rq3rvaicjb66-act-0.2.63"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/iwjzs1r05xrdk4xsw6a2c33436s08x18-act-0.2.63", "default": true}], "store_path": "/nix/store/iwjzs1r05xrdk4xsw6a2c33436s08x18-act-0.2.63"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/4d5qhzbrwm9k9yxnwlkwjn8740mh2hlz-act-0.2.63", "default": true}], "store_path": "/nix/store/4d5qhzbrwm9k9yxnwlkwjn8740mh2hlz-act-0.2.63"}}}, "civo@latest": {"last_modified": "2024-06-13T11:09:45Z", "resolved": "github:NixOS/nixpkgs/3f84a279f1a6290ce154c5531378acc827836fbb#civo", "source": "devbox-search", "version": "1.0.87", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/spl2wqr725gx94b1zbhqp4jzwp3i7sis-civo-1.0.87", "default": true}], "store_path": "/nix/store/spl2wqr725gx94b1zbhqp4jzwp3i7sis-civo-1.0.87"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/122j0cw7kxnxka5bdhrxjx70bx4v0l22-civo-1.0.87", "default": true}], "store_path": "/nix/store/122j0cw7kxnxka5bdhrxjx70bx4v0l22-civo-1.0.87"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/wsgyihx7zybd4r5zfi1yw9mn59665ja2-civo-1.0.87", "default": true}], "store_path": "/nix/store/wsgyihx7zybd4r5zfi1yw9mn59665ja2-civo-1.0.87"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/j03hhd3j2qxi8dz98fcsd991ibi6b1j4-civo-1.0.87", "default": true}], "store_path": "/nix/store/j03hhd3j2qxi8dz98fcsd991ibi6b1j4-civo-1.0.87"}}}, "envsubst@latest": {"last_modified": "2024-05-22T06:18:38Z", "resolved": "github:NixOS/nixpkgs/3f316d2a50699a78afe5e77ca486ad553169061e#envsubst", "source": "devbox-search", "version": "1.4.2", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/9fv03b9zy4nq1316njb2yc1i45ag3py9-envsubst-1.4.2", "default": true}], "store_path": "/nix/store/9fv03b9zy4nq1316njb2yc1i45ag3py9-envsubst-1.4.2"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/8a6gfs1wxmd1yady0rg0dc843sgpxn1f-envsubst-1.4.2", "default": true}], "store_path": "/nix/store/8a6gfs1wxmd1yady0rg0dc843sgpxn1f-envsubst-1.4.2"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/am9hsk62krixjq4qs6k56jn4ajcclh88-envsubst-1.4.2", "default": true}], "store_path": "/nix/store/am9hsk62krixjq4qs6k56jn4ajcclh88-envsubst-1.4.2"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/8jfd15711zjz6d7lib09j40arfn24z7z-envsubst-1.4.2", "default": true}], "store_path": "/nix/store/8jfd15711zjz6d7lib09j40arfn24z7z-envsubst-1.4.2"}}}, "gh@latest": {"last_modified": "2024-05-14T02:22:19Z", "resolved": "github:NixOS/nixpkgs/abd6d48f8c77bea7dc51beb2adfa6ed3950d2585#gh", "source": "devbox-search", "version": "2.49.2", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/hl1zg0d9dj9vg9nrqd55fskpdwxqbjpb-gh-2.49.2", "default": true}], "store_path": "/nix/store/hl1zg0d9dj9vg9nrqd55fskpdwxqbjpb-gh-2.49.2"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/sx6xcpj2vjj3vgpx1kxnigzi781nbs69-gh-2.49.2", "default": true}], "store_path": "/nix/store/sx6xcpj2vjj3vgpx1kxnigzi781nbs69-gh-2.49.2"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/fiphvxi33bsvibj6yr3v99f1b94fanz7-gh-2.49.2", "default": true}], "store_path": "/nix/store/fiphvxi33bsvibj6yr3v99f1b94fanz7-gh-2.49.2"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/p705fy2z5hwpfq18zx3v6772vwmz53yd-gh-2.49.2", "default": true}], "store_path": "/nix/store/p705fy2z5hwpfq18zx3v6772vwmz53yd-gh-2.49.2"}}}, "github:NixOS/nixpkgs/nixpkgs-unstable": {"resolved": "github:NixOS/nixpkgs/1128e89fd5e11bb25aedbfc287733c6502202ea9?lastModified=1739451785&narHash=sha256-3ebRdThRic9bHMuNi2IAA%2Fek9b32bsy8F5R4SvGTIog%3D"}, "go-task@latest": {"last_modified": "2024-05-17T18:08:26Z", "resolved": "github:NixOS/nixpkgs/0837fbf227364d79cbae8fff2378125526905cbe#go-task", "source": "devbox-search", "version": "3.37.2", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/yhc3mb45c6dy0p3714wf2c7iq8xkj5n7-go-task-3.37.2", "default": true}], "store_path": "/nix/store/yhc3mb45c6dy0p3714wf2c7iq8xkj5n7-go-task-3.37.2"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/v2bnnv9sffmvy6i2g6frs1im3ychz055-go-task-3.37.2", "default": true}], "store_path": "/nix/store/v2bnnv9sffmvy6i2g6frs1im3ychz055-go-task-3.37.2"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/cmdq0pkarnvhpc0cfmq2k0nmpz8ca4ah-go-task-3.37.2", "default": true}], "store_path": "/nix/store/cmdq0pkarnvhpc0cfmq2k0nmpz8ca4ah-go-task-3.37.2"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/qn4ib3ykjpljfl9vsaqbf32b99xjr25b-go-task-3.37.2", "default": true}], "store_path": "/nix/store/qn4ib3ykjpljfl9vsaqbf32b99xjr25b-go-task-3.37.2"}}}, "go@latest": {"last_modified": "2024-05-22T06:18:38Z", "resolved": "github:NixOS/nixpkgs/3f316d2a50699a78afe5e77ca486ad553169061e#go", "source": "devbox-search", "version": "1.22.3", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/i04a1a6qgxhjw6c0ld2b3x1v815sbxjc-go-1.22.3", "default": true}], "store_path": "/nix/store/i04a1a6qgxhjw6c0ld2b3x1v815sbxjc-go-1.22.3"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/d68f2iblysnl0r4qcfdacmdpmvvy86kf-go-1.22.3", "default": true}], "store_path": "/nix/store/d68f2iblysnl0r4qcfdacmdpmvvy86kf-go-1.22.3"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/1p6vr83cgyfwm8517jhfmf6lypzhy3q2-go-1.22.3", "default": true}], "store_path": "/nix/store/1p6vr83cgyfwm8517jhfmf6lypzhy3q2-go-1.22.3"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/00mg4vlhzmm7gi9bd5v5ydjlgrywpc3n-go-1.22.3", "default": true}], "store_path": "/nix/store/00mg4vlhzmm7gi9bd5v5ydjlgrywpc3n-go-1.22.3"}}}, "gum@latest": {"last_modified": "2024-05-12T16:19:40Z", "resolved": "github:NixOS/nixpkgs/3281bec7174f679eabf584591e75979a258d8c40#gum", "source": "devbox-search", "version": "0.14.0", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/bvszjmc0xgpx0xixjnn5nif7rdvsxy21-gum-0.14.0", "default": true}], "store_path": "/nix/store/bvszjmc0xgpx0xixjnn5nif7rdvsxy21-gum-0.14.0"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/29zbxd0ansmg0972k4yyhwbam3n0ab5k-gum-0.14.0", "default": true}], "store_path": "/nix/store/29zbxd0ansmg0972k4yyhwbam3n0ab5k-gum-0.14.0"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/chhwlcrq6w7fqb3fqb3nhakb3nms3vfb-gum-0.14.0", "default": true}], "store_path": "/nix/store/chhwlcrq6w7fqb3fqb3nhakb3nms3vfb-gum-0.14.0"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/3nh4kwsf4vwrjr7nffmwdair6rp9syy1-gum-0.14.0", "default": true}], "store_path": "/nix/store/3nh4kwsf4vwrjr7nffmwdair6rp9syy1-gum-0.14.0"}}}, "jq@latest": {"last_modified": "2024-05-21T17:27:28Z", "resolved": "github:NixOS/nixpkgs/e381a1288138aceda0ac63db32c7be545b446921#jq", "source": "devbox-search", "version": "1.7.1", "systems": {"aarch64-darwin": {"outputs": [{"name": "bin", "path": "/nix/store/2rf0h1lrb7npyl8c43hz516qsiws3a3i-jq-1.7.1-bin", "default": true}, {"name": "man", "path": "/nix/store/4la1j0wv0n0iqbzs0azn8jw66a9npczm-jq-1.7.1-man", "default": true}, {"name": "out", "path": "/nix/store/0q8b43a5ysgr8cmw1f0z9zjs4g154pq2-jq-1.7.1"}, {"name": "dev", "path": "/nix/store/4a2fm58fcjmlm55kvzpw6hbr94zz62p2-jq-1.7.1-dev"}, {"name": "doc", "path": "/nix/store/kggwwxvqvlb7wpmysqp8zjfm6hm9gb2s-jq-1.7.1-doc"}, {"name": "lib", "path": "/nix/store/7qxv4s17qgx1h5a1wbd1qi85lm27k665-jq-1.7.1-lib"}], "store_path": "/nix/store/2rf0h1lrb7npyl8c43hz516qsiws3a3i-jq-1.7.1-bin"}, "aarch64-linux": {"outputs": [{"name": "bin", "path": "/nix/store/1jgvvjai94qd0ca0l2xcmnn9nrr4b8cm-jq-1.7.1-bin", "default": true}, {"name": "man", "path": "/nix/store/yzlrqhi86fx1xad0n0j951c23n8qn6ng-jq-1.7.1-man", "default": true}, {"name": "dev", "path": "/nix/store/g4pfmd2p3a7kcjqybwjlvngc1dl397hk-jq-1.7.1-dev"}, {"name": "doc", "path": "/nix/store/3wfn3fpd82bzd7x9wwazh9rvrg5xkpza-jq-1.7.1-doc"}, {"name": "lib", "path": "/nix/store/560f44gmk3cy5p9qlxpw69y372nb95i3-jq-1.7.1-lib"}, {"name": "out", "path": "/nix/store/9815jk0vnl2ndik2zi3iyai62w3kvcan-jq-1.7.1"}], "store_path": "/nix/store/1jgvvjai94qd0ca0l2xcmnn9nrr4b8cm-jq-1.7.1-bin"}, "x86_64-darwin": {"outputs": [{"name": "bin", "path": "/nix/store/6c413vbraq85ijh7ymjzqb2hgkg202wj-jq-1.7.1-bin", "default": true}, {"name": "man", "path": "/nix/store/qdmy9svjkf6hdwqz4isnrf1lwjl43lvn-jq-1.7.1-man", "default": true}, {"name": "out", "path": "/nix/store/kprb5lyjwlkbzyjkvhndzj9lmsh40mhj-jq-1.7.1"}, {"name": "dev", "path": "/nix/store/4wy9mygmhirpi0lrplan7w8sc0lwrkyw-jq-1.7.1-dev"}, {"name": "doc", "path": "/nix/store/9xjixsdagz91dgk2x47160vq8k4hlyh3-jq-1.7.1-doc"}, {"name": "lib", "path": "/nix/store/2l58lhi2g2dmx5qbf0wss4jj4jb8dcq8-jq-1.7.1-lib"}], "store_path": "/nix/store/6c413vbraq85ijh7ymjzqb2hgkg202wj-jq-1.7.1-bin"}, "x86_64-linux": {"outputs": [{"name": "bin", "path": "/nix/store/f1xhpw3m2155k1y3ylgiaknm2g7g7r50-jq-1.7.1-bin", "default": true}, {"name": "man", "path": "/nix/store/2ab43a1k12sb9l5qf77vd9rlj3k3qap1-jq-1.7.1-man", "default": true}, {"name": "doc", "path": "/nix/store/8vkapd7w7dhcs2a33pm1dihizd314h5b-jq-1.7.1-doc"}, {"name": "lib", "path": "/nix/store/2kaw6d2xbilbzg7h8r3310zmic0kn7pj-jq-1.7.1-lib"}, {"name": "out", "path": "/nix/store/jrb6j0b9c7bfr12lrrb3x84ijgfh2w3g-jq-1.7.1"}, {"name": "dev", "path": "/nix/store/wpayb7lis1r3qsw2kxpjrqiri3318mlv-jq-1.7.1-dev"}], "store_path": "/nix/store/f1xhpw3m2155k1y3ylgiaknm2g7g7r50-jq-1.7.1-bin"}}}, "k9s@latest": {"last_modified": "2024-05-12T16:19:40Z", "resolved": "github:NixOS/nixpkgs/3281bec7174f679eabf584591e75979a258d8c40#k9s", "source": "devbox-search", "version": "0.32.4", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/2bffbcn14y0xg67q5wb3v6nmjgqvybcs-k9s-0.32.4", "default": true}], "store_path": "/nix/store/2bffbcn14y0xg67q5wb3v6nmjgqvybcs-k9s-0.32.4"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/v2xj3pynlg36dbwzg7y89zvvg411l1lq-k9s-0.32.4", "default": true}], "store_path": "/nix/store/v2xj3pynlg36dbwzg7y89zvvg411l1lq-k9s-0.32.4"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/7m660bzv2arzk536hnihzrhr1cqj54b2-k9s-0.32.4", "default": true}], "store_path": "/nix/store/7m660bzv2arzk536hnihzrhr1cqj54b2-k9s-0.32.4"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/bxv26x8c5610kzy8mfbg87a793g97haf-k9s-0.32.4", "default": true}], "store_path": "/nix/store/bxv26x8c5610kzy8mfbg87a793g97haf-k9s-0.32.4"}}}, "kind@latest": {"last_modified": "2024-05-17T18:08:26Z", "resolved": "github:NixOS/nixpkgs/0837fbf227364d79cbae8fff2378125526905cbe#kind", "source": "devbox-search", "version": "0.23.0", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/rh1v9qz7y8jq4dcym86awhkkja4n5y11-kind-0.23.0", "default": true}], "store_path": "/nix/store/rh1v9qz7y8jq4dcym86awhkkja4n5y11-kind-0.23.0"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/qifqagwjp8h06rl9j4mr811hhj4zcji8-kind-0.23.0", "default": true}], "store_path": "/nix/store/qifqagwjp8h06rl9j4mr811hhj4zcji8-kind-0.23.0"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/lsbspyygayrxywml0yh63lj2csmgkp0g-kind-0.23.0", "default": true}], "store_path": "/nix/store/lsbspyygayrxywml0yh63lj2csmgkp0g-kind-0.23.0"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/9v6avz3fdajzlbjbvmz0nysmdjhklqq1-kind-0.23.0", "default": true}], "store_path": "/nix/store/9v6avz3fdajzlbjbvmz0nysmdjhklqq1-kind-0.23.0"}}}, "kluctl@latest": {"last_modified": "2024-07-18T11:48:29Z", "resolved": "github:NixOS/nixpkgs/7e2fb8e0eb807e139d42b05bf8e28da122396bed#kluctl", "source": "devbox-search", "version": "2.25.0", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/5p082mshnkcdzarfjd9204fvrxjvh6d0-kluctl-2.25.0", "default": true}], "store_path": "/nix/store/5p082mshnkcdzarfjd9204fvrxjvh6d0-kluctl-2.25.0"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/y83a4l10i5sk441abw20y134wja7ljsd-kluctl-2.25.0", "default": true}], "store_path": "/nix/store/y83a4l10i5sk441abw20y134wja7ljsd-kluctl-2.25.0"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/b3kabiwgypv86y7smarc9kwkajpgll9a-kluctl-2.25.0", "default": true}], "store_path": "/nix/store/b3kabiwgypv86y7smarc9kwkajpgll9a-kluctl-2.25.0"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/pf0w5a4r09g6djw71rycgm2ifgbsm1jy-kluctl-2.25.0", "default": true}], "store_path": "/nix/store/pf0w5a4r09g6djw71rycgm2ifgbsm1jy-kluctl-2.25.0"}}}, "ko@latest": {"last_modified": "2024-05-22T06:18:38Z", "resolved": "github:NixOS/nixpkgs/3f316d2a50699a78afe5e77ca486ad553169061e#ko", "source": "devbox-search", "version": "0.15.4", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/7y8xi4h622j2w2wdf2qvya7z766ll1wl-ko-0.15.4", "default": true}], "store_path": "/nix/store/7y8xi4h622j2w2wdf2qvya7z766ll1wl-ko-0.15.4"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/mgjchinyl6ilzcs7ww295m55i9121kkf-ko-0.15.4", "default": true}], "store_path": "/nix/store/mgjchinyl6ilzcs7ww295m55i9121kkf-ko-0.15.4"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/fqlibvj9qdm4221g7wg4mfqvaw2l8l1k-ko-0.15.4", "default": true}], "store_path": "/nix/store/fqlibvj9qdm4221g7wg4mfqvaw2l8l1k-ko-0.15.4"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/vi97ix6hlnkwsigic4pg8pmcairlh6my-ko-0.15.4", "default": true}], "store_path": "/nix/store/vi97ix6hlnkwsigic4pg8pmcairlh6my-ko-0.15.4"}}}, "kubectl@latest": {"last_modified": "2024-05-17T18:08:26Z", "resolved": "github:NixOS/nixpkgs/0837fbf227364d79cbae8fff2378125526905cbe#kubectl", "source": "devbox-search", "version": "1.30.1", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/wrpbbz77yk84r5pk36fn469q5y9hpxms-kubectl-1.30.1", "default": true}, {"name": "man", "path": "/nix/store/wvrbv96r915ssaw09c9wj8xsqp0rg0x2-kubectl-1.30.1-man", "default": true}, {"name": "convert", "path": "/nix/store/z27q44mkfssb704nx0168lsqlxz5vy8a-kubectl-1.30.1-convert"}], "store_path": "/nix/store/wrpbbz77yk84r5pk36fn469q5y9hpxms-kubectl-1.30.1"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/xah8iz7brfms9zllnaf1rpmf9ihn41za-kubectl-1.30.1", "default": true}, {"name": "man", "path": "/nix/store/b1903js3paa8lwjsdcd68pv43pwxnl9a-kubectl-1.30.1-man", "default": true}, {"name": "convert", "path": "/nix/store/iwvm4ybhxn00bhmskjgq601051rkvphx-kubectl-1.30.1-convert"}], "store_path": "/nix/store/xah8iz7brfms9zllnaf1rpmf9ihn41za-kubectl-1.30.1"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/c4q06h9w6288lcjp8vj30gkvqwjjwqaz-kubectl-1.30.1", "default": true}, {"name": "man", "path": "/nix/store/31wm58wiinklq5rva4lpycm2mwhlv606-kubectl-1.30.1-man", "default": true}, {"name": "convert", "path": "/nix/store/qjspnv76bwlj956jni1j62yq7qh261gq-kubectl-1.30.1-convert"}], "store_path": "/nix/store/c4q06h9w6288lcjp8vj30gkvqwjjwqaz-kubectl-1.30.1"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/faakmnmx9q8nhi890f4zsls4js4b8pfy-kubectl-1.30.1", "default": true}, {"name": "man", "path": "/nix/store/aiszaxdqhn41rfb3y7z2853il1qp01sc-kubectl-1.30.1-man", "default": true}, {"name": "convert", "path": "/nix/store/bp7lqx3ibnc01f73y0z7yy80lwk108gy-kubectl-1.30.1-convert"}], "store_path": "/nix/store/faakmnmx9q8nhi890f4zsls4js4b8pfy-kubectl-1.30.1"}}}, "kubectx@latest": {"last_modified": "2024-05-12T16:19:40Z", "resolved": "github:NixOS/nixpkgs/3281bec7174f679eabf584591e75979a258d8c40#kubectx", "source": "devbox-search", "version": "0.9.5", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/pvlrgm2257a0zqa1bp2nxi4pw998scxg-kubectx-0.9.5", "default": true}], "store_path": "/nix/store/pvlrgm2257a0zqa1bp2nxi4pw998scxg-kubectx-0.9.5"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/zd0ax1bj438idm8vx1ghx1fhzpi8m07l-kubectx-0.9.5", "default": true}], "store_path": "/nix/store/zd0ax1bj438idm8vx1ghx1fhzpi8m07l-kubectx-0.9.5"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/1hfz4jx8r080m8z8cnpqc80nc6ibilc3-kubectx-0.9.5", "default": true}], "store_path": "/nix/store/1hfz4jx8r080m8z8cnpqc80nc6ibilc3-kubectx-0.9.5"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/7rpri54nfphdranqgn6srz87jrsvflqx-kubectx-0.9.5", "default": true}], "store_path": "/nix/store/7rpri54nfphdranqgn6srz87jrsvflqx-kubectx-0.9.5"}}}, "kubent@latest": {"last_modified": "2024-05-29T10:04:41Z", "resolved": "github:NixOS/nixpkgs/ac82a513e55582291805d6f09d35b6d8b60637a1#kubent", "source": "devbox-search", "version": "0.7.2", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/x6mh8rjrgc75wirlq4v2r5zajrhj81v2-kubent-0.7.2", "default": true}], "store_path": "/nix/store/x6mh8rjrgc75wirlq4v2r5zajrhj81v2-kubent-0.7.2"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/zhlb08gy356n04z4ryrhv2vgqadk19a0-kubent-0.7.2", "default": true}], "store_path": "/nix/store/zhlb08gy356n04z4ryrhv2vgqadk19a0-kubent-0.7.2"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/nzd33ajrv8pnrvcjzw58wykw57v4s6k8-kubent-0.7.2", "default": true}], "store_path": "/nix/store/nzd33ajrv8pnrvcjzw58wykw57v4s6k8-kubent-0.7.2"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/hxm0jjmfh745pbd7qrbnpza57gv98ac2-kubent-0.7.2", "default": true}], "store_path": "/nix/store/hxm0jjmfh745pbd7qrbnpza57gv98ac2-kubent-0.7.2"}}}, "kubernetes-helm@latest": {"last_modified": "2024-05-17T18:08:26Z", "resolved": "github:NixOS/nixpkgs/0837fbf227364d79cbae8fff2378125526905cbe#kubernetes-helm", "source": "devbox-search", "version": "3.15.0", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/i8bdqpy09lp9m83q1n6xc9s02i6xplxn-kubernetes-helm-3.15.0", "default": true}], "store_path": "/nix/store/i8bdqpy09lp9m83q1n6xc9s02i6xplxn-kubernetes-helm-3.15.0"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/qwgrbp9qs2mjw2mfn4w0dx4m5575dzn6-kuberne<PERSON>-helm-3.15.0", "default": true}], "store_path": "/nix/store/qwgrbp9qs2mjw2mfn4w0dx4m5575dzn6-kuberne<PERSON>-helm-3.15.0"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/alk0ib91zx345hc1jaz7vwm9r3jxgwn4-kuberne<PERSON>-helm-3.15.0", "default": true}], "store_path": "/nix/store/alk0ib91zx345hc1jaz7vwm9r3jxgwn4-kuberne<PERSON>-helm-3.15.0"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/w6sfmk0id016mf2c32hi2xn2j0kd9jk0-kuberne<PERSON>-helm-3.15.0", "default": true}], "store_path": "/nix/store/w6sfmk0id016mf2c32hi2xn2j0kd9jk0-kuberne<PERSON>-helm-3.15.0"}}}, "kustomize@latest": {"last_modified": "2024-05-29T10:04:41Z", "resolved": "github:NixOS/nixpkgs/ac82a513e55582291805d6f09d35b6d8b60637a1#kustomize", "source": "devbox-search", "version": "5.4.1", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/rn0bmhs7b22ryanwynm061hr3m1mnpaf-kustomize-5.4.1", "default": true}], "store_path": "/nix/store/rn0bmhs7b22ryanwynm061hr3m1mnpaf-kustomize-5.4.1"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/jm1y98gpslzzsvq600l0brr1jlqak4q5-kustomize-5.4.1", "default": true}], "store_path": "/nix/store/jm1y98gpslzzsvq600l0brr1jlqak4q5-kustomize-5.4.1"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/dps11nvz1jz92j4nq50zxcpa2477pqa1-kustomize-5.4.1", "default": true}], "store_path": "/nix/store/dps11nvz1jz92j4nq50zxcpa2477pqa1-kustomize-5.4.1"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/brk03k57nrv2smfaqnpzb78k0ibrcxfa-kustomize-5.4.1", "default": true}], "store_path": "/nix/store/brk03k57nrv2smfaqnpzb78k0ibrcxfa-kustomize-5.4.1"}}}, "nodejs_20@latest": {"last_modified": "2024-05-22T06:18:38Z", "plugin_version": "0.0.2", "resolved": "github:NixOS/nixpkgs/3f316d2a50699a78afe5e77ca486ad553169061e#nodejs_20", "source": "devbox-search", "version": "20.12.2", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/bzzs4kvjyvjjhs3rj08vqpvvzmfggvbv-nodejs-20.12.2", "default": true}, {"name": "libv8", "path": "/nix/store/c56874bxzncqwy58kif6wfnzy017v1sl-nodejs-20.12.2-libv8"}], "store_path": "/nix/store/bzzs4kvjyvjjhs3rj08vqpvvzmfggvbv-nodejs-20.12.2"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/y50zafzgnnkrj4hvmk23icv2ggvys8r9-nodejs-20.12.2", "default": true}, {"name": "libv8", "path": "/nix/store/vc7y8h3c8pwbh4zbvjcyfqrd3fhdjhw6-nodejs-20.12.2-libv8"}], "store_path": "/nix/store/y50zafzgnnkrj4hvmk23icv2ggvys8r9-nodejs-20.12.2"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/l53svh1nfrcb83qbqvrrkangrcl1rr25-nodejs-20.12.2", "default": true}, {"name": "libv8", "path": "/nix/store/q71hh22bfqjygd34gq16dv4dwfc33378-nodejs-20.12.2-libv8"}], "store_path": "/nix/store/l53svh1nfrcb83qbqvrrkangrcl1rr25-nodejs-20.12.2"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/6g9n96qf1yx139xklnmy3v4xhjvjgsji-nodejs-20.12.2", "default": true}, {"name": "libv8", "path": "/nix/store/s7b0dqga0311mvq48mirnlm0p3dr4gm3-nodejs-20.12.2-libv8"}], "store_path": "/nix/store/6g9n96qf1yx139xklnmy3v4xhjvjgsji-nodejs-20.12.2"}}}, "oras@latest": {"last_modified": "2024-05-12T16:19:40Z", "resolved": "github:NixOS/nixpkgs/3281bec7174f679eabf584591e75979a258d8c40#oras", "source": "devbox-search", "version": "1.1.0", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/5x67asmql4lb5g7i1zk4nx3y55lq67rk-oras-1.1.0", "default": true}], "store_path": "/nix/store/5x67asmql4lb5g7i1zk4nx3y55lq67rk-oras-1.1.0"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/6k0n6k6qfz248nvd4rkq576xr9yr8hl7-oras-1.1.0", "default": true}], "store_path": "/nix/store/6k0n6k6qfz248nvd4rkq576xr9yr8hl7-oras-1.1.0"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/iyghcr1rhjb99xz6hqv3hjcp7h8hl0qa-oras-1.1.0", "default": true}], "store_path": "/nix/store/iyghcr1rhjb99xz6hqv3hjcp7h8hl0qa-oras-1.1.0"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/0ss867aj1w63zzr6bvdkgr2xy5l6h84x-oras-1.1.0", "default": true}], "store_path": "/nix/store/0ss867aj1w63zzr6bvdkgr2xy5l6h84x-oras-1.1.0"}}}, "poetry@latest": {"last_modified": "2024-05-22T06:18:38Z", "plugin_version": "0.0.4", "resolved": "github:NixOS/nixpkgs/3f316d2a50699a78afe5e77ca486ad553169061e#poetry", "source": "devbox-search", "version": "1.8.3", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/3c7njb9l3jk7v0ldzdycspywq54nbbbz-python3.11-poetry-1.8.3", "default": true}, {"name": "dist", "path": "/nix/store/mdqd4zbp8rgpz4y4f7y5jcf99ls1h4nl-python3.11-poetry-1.8.3-dist"}], "store_path": "/nix/store/3c7njb9l3jk7v0ldzdycspywq54nbbbz-python3.11-poetry-1.8.3"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/byjijql0mz755bdmvz9jmmfcylbrmx59-python3.11-poetry-1.8.3", "default": true}, {"name": "dist", "path": "/nix/store/6s2dc9slg8v1dk3bdh7yk1fxjmxsxjf2-python3.11-poetry-1.8.3-dist"}], "store_path": "/nix/store/byjijql0mz755bdmvz9jmmfcylbrmx59-python3.11-poetry-1.8.3"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/pqrrq8xsii7kzscacnnwz9zz4nld6dsd-python3.11-poetry-1.8.3", "default": true}, {"name": "dist", "path": "/nix/store/0vh69hrjj7sh92ssmwrr7312pisnh6di-python3.11-poetry-1.8.3-dist"}], "store_path": "/nix/store/pqrrq8xsii7kzscacnnwz9zz4nld6dsd-python3.11-poetry-1.8.3"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/vi424968j3nx0wq96vagrij3zv4z7hs9-python3.11-poetry-1.8.3", "default": true}, {"name": "dist", "path": "/nix/store/wqpr3zi1saxva16k92cvw77369081vzs-python3.11-poetry-1.8.3-dist"}], "store_path": "/nix/store/vi424968j3nx0wq96vagrij3zv4z7hs9-python3.11-poetry-1.8.3"}}}, "python312@latest": {"last_modified": "2024-05-22T06:18:38Z", "plugin_version": "0.0.4", "resolved": "github:NixOS/nixpkgs/3f316d2a50699a78afe5e77ca486ad553169061e#python312", "source": "devbox-search", "version": "3.12.3", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/lgymkqydhhmf88rvbq6hagq8lcpfr52w-python3-3.12.3", "default": true}], "store_path": "/nix/store/lgymkqydhhmf88rvbq6hagq8lcpfr52w-python3-3.12.3"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/5wr6cnizn2by0m6px4cqmcny6wj63j8x-python3-3.12.3", "default": true}, {"name": "debug", "path": "/nix/store/l2893yj1z369wsw13xngjjjipf2rwh8w-python3-3.12.3-debug"}], "store_path": "/nix/store/5wr6cnizn2by0m6px4cqmcny6wj63j8x-python3-3.12.3"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/cq1758383k2kn3qvggyy82cfy5h1jnxa-python3-3.12.3", "default": true}], "store_path": "/nix/store/cq1758383k2kn3qvggyy82cfy5h1jnxa-python3-3.12.3"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/c7ycrgwv039nqglbif98yggx211sdbcl-python3-3.12.3", "default": true}, {"name": "debug", "path": "/nix/store/5s5599pm38hpclwnnc4yf896fbbk8j4y-python3-3.12.3-debug"}], "store_path": "/nix/store/c7ycrgwv039nqglbif98yggx211sdbcl-python3-3.12.3"}}}, "tilt@latest": {"last_modified": "2024-05-29T10:04:41Z", "resolved": "github:NixOS/nixpkgs/ac82a513e55582291805d6f09d35b6d8b60637a1#tilt", "source": "devbox-search", "version": "0.33.10", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/qjzcr1js355d5hkbm8whnbi60w2q4wgv-tilt-0.33.10", "default": true}], "store_path": "/nix/store/qjzcr1js355d5hkbm8whnbi60w2q4wgv-tilt-0.33.10"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/mxb1gh3sm1hqs5wqbxb0vbv0ysmd36j8-tilt-0.33.10", "default": true}], "store_path": "/nix/store/mxb1gh3sm1hqs5wqbxb0vbv0ysmd36j8-tilt-0.33.10"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/x1z0j1chrgvzhmm8q11zf2d6w6i53iay-tilt-0.33.10", "default": true}], "store_path": "/nix/store/x1z0j1chrgvzhmm8q11zf2d6w6i53iay-tilt-0.33.10"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/8n7f6jcn5zw7xh8vcgiaamxy8aswwvhq-tilt-0.33.10", "default": true}], "store_path": "/nix/store/8n7f6jcn5zw7xh8vcgiaamxy8aswwvhq-tilt-0.33.10"}}}, "yq-go@latest": {"last_modified": "2024-05-12T16:19:40Z", "resolved": "github:NixOS/nixpkgs/3281bec7174f679eabf584591e75979a258d8c40#yq-go", "source": "devbox-search", "version": "4.44.1", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/4qfgmglilwi7lw4qgplrjazgcy1xd2zf-yq-go-4.44.1", "default": true}], "store_path": "/nix/store/4qfgmglilwi7lw4qgplrjazgcy1xd2zf-yq-go-4.44.1"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/f2qnkmvwjvhxwn6haqzcd2s41vsrn3j4-yq-go-4.44.1", "default": true}], "store_path": "/nix/store/f2qnkmvwjvhxwn6haqzcd2s41vsrn3j4-yq-go-4.44.1"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/3793i8z8bf24flrh42q2k4nrw6pmqy6s-yq-go-4.44.1", "default": true}], "store_path": "/nix/store/3793i8z8bf24flrh42q2k4nrw6pmqy6s-yq-go-4.44.1"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/48yjcqr6i5wgsgfis4ji3xdjjy7vy3qf-yq-go-4.44.1", "default": true}], "store_path": "/nix/store/48yjcqr6i5wgsgfis4ji3xdjjy7vy3qf-yq-go-4.44.1"}}}}}