apiVersion: apps/v1
kind: Deployment
metadata:
  name: shared-pvc-kind
spec:
  # Both pods can only run simultaneously on the same node because
  # PVC has accessModes: [ReadWriteOnce]. Would be better to demo this
  # on multi-node cluster
  replicas: 2
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
        - name: nginx
          image: nginx:1.26.0
          volumeMounts:
            - name: data
              mountPath: /some/mount/path
      volumes:
        - name: data
          persistentVolumeClaim:
            claimName: dynamic-pv-kind
