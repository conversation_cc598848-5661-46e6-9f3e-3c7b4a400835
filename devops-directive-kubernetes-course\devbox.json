{"$schema": "https://raw.githubusercontent.com/jetify-com/devbox/0.10.7/.schema/devbox.schema.json", "packages": ["civo@latest", "envsubst@latest", "gh@latest", "go@latest", "go-task@latest", "path:gcloud#google-cloud-sdk", "gum@latest", "jq@latest", "k9s@latest", "kind@latest", "kluctl@latest", "ko@latest", "kubectl@latest", "kubectx@latest", "kuberne<PERSON>-helm@latest", "kustomize@latest", "oras@latest", "nodejs_20@latest", "poetry@latest", "python312@latest", "tilt@latest", "yq-go@latest", "act@latest", "kubent@latest"], "shell": {"init_hook": ["export GOBIN=$(git rev-parse --show-toplevel)/bin", "export PATH=$GOBIN:$PATH", "go install sigs.k8s.io/cloud-provider-kind@v0.2.0"], "scripts": {"test": ["echo \"Error: no test specified\" && exit 1"]}}}