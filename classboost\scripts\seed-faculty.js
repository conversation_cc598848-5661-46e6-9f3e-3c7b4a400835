const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// MongoDB connection
const MONGODB_URI = "mongodb+srv://infernogane:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";

// Define schemas
const FacultySchema = new mongoose.Schema({
  fullName: String,
  email: String,
  password: String,
  department: String,
  subjects: [String],
  role: String,
  createdAt: { type: Date, default: Date.now },
});

// Create models
const Faculty = mongoose.model('Faculty', FacultySchema);

// Sample data
const sampleFaculty = [
  {
    fullName: 'Dr. <PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    department: 'Computer Science',
    subjects: ['Mathematics', 'Computer Science', 'Programming'],
    role: 'faculty',
  },
  {
    fullName: 'Prof. <PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    department: 'Information Technology',
    subjects: ['Database Systems', 'Web Development', 'Information Security'],
    role: 'faculty',
  },
];

// Seed function
async function seedDatabase() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Check if faculty already exists
    const existingFaculty = await Faculty.findOne({ email: sampleFaculty[0].email });
    
    if (existingFaculty) {
      console.log('Faculty data already exists, skipping seed');
      return;
    }

    // Hash passwords for faculty
    const hashedFaculty = await Promise.all(
      sampleFaculty.map(async (faculty) => {
        const hashedPassword = await bcrypt.hash(faculty.password, 10);
        return { ...faculty, password: hashedPassword };
      })
    );

    // Insert sample data
    await Faculty.insertMany(hashedFaculty);

    console.log('Faculty data inserted successfully');
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
}

// Run the seed function
seedDatabase();
