{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@repo/ui": "*", "next": "^15.1.6", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/typescript-config": "*", "@types/node": "^22", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "eslint": "^9.21.0", "typescript": "5.7.3"}}