apiVersion: v1
kind: Pod
metadata:
  name: secret-example
spec:
  containers:
    - name: nginx
      image: nginx:1.26.0
      volumeMounts:
        - name: secret-base64-data
          mountPath: /etc/config
      env:
        - name: ENV_VAR_FROM_SECRET
          valueFrom:
            secretKeyRef:
              name: base64-data
              key: foo
  imagePullSecrets:
    # Not necessary since example uses a public image, but including to show how
    # you would use a registry credential secret to access a private image
    - name: dockerconfigjson
  volumes:
    - name: secret-base64-data
      secret:
        secretName: base64-data
