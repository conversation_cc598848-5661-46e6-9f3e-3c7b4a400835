// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}


model User {
id        String   @id @default(uuid())
email     String   @unique
createdAt DateTime @default(now())
updatedAt DateTime @updatedAt
}

model Project{
  id            String   @id @default(uuid())
description     String? 
createdAt       DateTime @default(now())
updatedAt       DateTime @updatedAt
prompt          Prompt[]
userId          String
}

model Prompt {
    id            String   @id @default(uuid())
content           String
createdAt       DateTime @default(now())
updatedAt       DateTime @updatedAt
project          Project @relation(fields: [projectId],references: [id])
projectId       String
}