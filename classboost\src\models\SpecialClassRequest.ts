import mongoose from 'mongoose';

const SpecialClassRequestSchema = new mongoose.Schema({
  studentEmail: {
    type: String,
    required: [true, 'Student email is required'],
    ref: 'Student',
  },
  subject: {
    type: String,
    required: [true, 'Subject name is required'],
  },
  reason: {
    type: String,
    required: [true, 'Reason is required'],
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending',
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

export default mongoose.models.SpecialClassRequest || mongoose.model('SpecialClassRequest', SpecialClassRequestSchema);
