import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import connectToDatabase from '@/lib/mongodb';
import Student from '@/models/Student';
import { handler as authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getToken } from 'next-auth/jwt';

export async function GET(req: NextRequest) {
  try {
    // Try both methods to get the session
    let session = await getServerSession(authOptions);

    // If session is not available, try to get the token directly
    if (!session || !session.user) {
      const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET || 'your-secret-key' });

      if (!token || !token.email) {
        console.error('Authentication failed: No valid session or token found');
        return NextResponse.json(
          { message: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Create a session-like object from the token
      session = {
        user: {
          email: token.email as string,
          id: token.id as string,
        }
      };
    }

    await connectToDatabase();

    const student = await Student.findOne({ email: session.user.email });

    if (!student) {
      return NextResponse.json(
        { message: 'Student not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      fullName: student.fullName,
      email: student.email,
      rollNumber: student.rollNumber,
      department: student.department,
      semester: student.semester,
      profileCompleted: student.profileCompleted,
    });
  } catch (error) {
    console.error('Profile fetch error:', error);
    return NextResponse.json(
      { message: 'Error fetching profile' },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    // Try both methods to get the session
    let session = await getServerSession(authOptions);

    // If session is not available, try to get the token directly
    if (!session || !session.user) {
      const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET || 'your-secret-key' });

      if (!token || !token.email) {
        console.error('Authentication failed: No valid session or token found');
        return NextResponse.json(
          { message: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Create a session-like object from the token
      session = {
        user: {
          email: token.email as string,
          id: token.id as string,
        }
      };
    }

    const { fullName, rollNumber, department, semester } = await req.json();

    // Validate input
    if (!fullName || !rollNumber || !department || !semester) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    await connectToDatabase();

    const updatedStudent = await Student.findOneAndUpdate(
      { email: session.user.email },
      {
        fullName,
        rollNumber,
        department,
        semester,
        profileCompleted: true
      },
      { new: true }
    );

    if (!updatedStudent) {
      return NextResponse.json(
        { message: 'Student not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Profile updated successfully',
      student: {
        fullName: updatedStudent.fullName,
        email: updatedStudent.email,
        rollNumber: updatedStudent.rollNumber,
        department: updatedStudent.department,
        semester: updatedStudent.semester,
        profileCompleted: updatedStudent.profileCompleted,
      }
    });
  } catch (error) {
    console.error('Profile update error:', error);
    return NextResponse.json(
      { message: 'Error updating profile' },
      { status: 500 }
    );
  }
}
