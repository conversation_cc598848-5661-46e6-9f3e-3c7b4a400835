import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { getToken } from 'next-auth/jwt';
import connectToDatabase from '@/lib/mongodb';
import Student from '@/models/Student';
import SpecialClassRequest from '@/models/SpecialClassRequest';
import { handler as authOptions } from '@/app/api/auth/[...nextauth]/route';
import mongoose from 'mongoose';

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Try both methods to get the session
    let session = await getServerSession(authOptions);
    
    // If session is not available, try to get the token directly
    if (!session || !session.user) {
      const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET || 'your-secret-key' });
      
      if (!token || !token.email || token.role !== 'faculty') {
        console.error('Authentication failed: No valid faculty session or token found');
        return NextResponse.json(
          { message: 'Unauthorized' },
          { status: 401 }
        );
      }
      
      // Create a session-like object from the token
      session = {
        user: {
          email: token.email as string,
          id: token.id as string,
          role: 'faculty',
        }
      };
    } else if (session.user.role !== 'faculty') {
      return NextResponse.json(
        { message: 'Unauthorized: Not a faculty member' },
        { status: 403 }
      );
    }

    // Validate the student ID
    if (!params.id || !mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { message: 'Invalid student ID' },
        { status: 400 }
      );
    }

    await connectToDatabase();

    // Get student to find email
    const student = await Student.findById(params.id);
    
    if (!student) {
      return NextResponse.json(
        { message: 'Student not found' },
        { status: 404 }
      );
    }

    // Get special class requests for the student
    const requests = await SpecialClassRequest.find({ 
      studentEmail: student.email 
    }).sort({ createdAt: -1 });

    return NextResponse.json(requests);
  } catch (error) {
    console.error('Student special class requests fetch error:', error);
    return NextResponse.json(
      { message: 'Error fetching student special class requests' },
      { status: 500 }
    );
  }
}
