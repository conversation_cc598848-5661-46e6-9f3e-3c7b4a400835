apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: minimal-gke
  annotations:
    kubernetes.io/ingress.class: "gce"
    # kubernetes.io/ingress.class: "gce-internal" (for traffic external to cluster but internal to VPC)
spec:
  # NOTE: You can't use spec.ingressClassName for GKE ingress
  rules:
    - host: "ingress-example-gke.com"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: nginx-nodeport
                port:
                  number: 80
