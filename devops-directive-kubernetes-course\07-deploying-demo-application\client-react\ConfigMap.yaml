apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-conf
  namespace: demo-app
data:
  default.conf: |-
    server {
      listen 8080;
      
      location /ping {
            access_log off;
            add_header 'Content-Type' 'text/plain';
            return 200 "pong";
      }
      
      location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html =404;
      }
      
      include /etc/nginx/extra-conf.d/*.conf;
    }
