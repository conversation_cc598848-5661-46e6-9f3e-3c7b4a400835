name: image-ci

on:
  push:
    branches:
      - "main"
    tags:
      - "[0-9]*.[0-9]*.[0-9]*"
    paths:
      # Only rebuild images when applications change
      - "06-demo-application/**/*"

jobs:
  generate-image-tag:
    runs-on: ubuntu-latest
    outputs:
      image_tag: ${{ steps.generate-image-tag.outputs.image_tag }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          # Necessary to get all tags for IMAGE_TAG generation with git describe
          fetch-depth: 0

      - name: Install Task
        uses: arduino/setup-task@v2
        with:
          version: 3.x

      - name: Generate Image Tag
        id: generate-image-tag
        working-directory: 14-cicd/github-actions
        run: |
          image_tag=$(task generate-version-tag)
          echo "image_tag=$image_tag" >> $GITHUB_OUTPUT

  build-tag-push:
    runs-on: ubuntu-latest
    needs: generate-image-tag
    strategy:
      matrix:
        path:
          - 06-demo-application/api-golang
          - 06-demo-application/api-node
          - 06-demo-application/client-react
          - 06-demo-application/load-generator-python
          - 06-demo-application/postgresql

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Task
        uses: arduino/setup-task@v2
        with:
          version: 3.x

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - uses: actions/setup-go@v5
        if: matrix.path == '06-demo-application/api-golang'
        with:
          go-version: "1.22.x"

      - uses: ko-build/setup-ko@v0.6
        if: matrix.path == '06-demo-application/api-golang'
        with:
          version: v0.15.4

      - name: Build Image
        env:
          IMAGE_TAG: ${{ needs.generate-image-tag.outputs.image_tag }}
        working-directory: ${{ matrix.path }}
        run: |
          task build-container-image-multi-arch IMAGE_TAG=${IMAGE_TAG}

  update-tags:
    runs-on: ubuntu-latest
    needs: [generate-image-tag, build-tag-push]

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Task
        uses: arduino/setup-task@v2
        with:
          version: 3.x

      - name: Update Image Tags
        working-directory: 14-cicd/github-actions
        env:
          IMAGE_TAG: ${{ needs.generate-image-tag.outputs.image_tag }}
        run: |
          # Update staging tags for push to main or release tag
          task update-staging-image-tags NEW_TAG=${IMAGE_TAG}            

          # Update production tags only for release tags
          if [[ $GITHUB_REF == refs/tags/*.*.* ]]; then
            task update-production-image-tags NEW_TAG=${IMAGE_TAG}
          fi

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v6
        with:
          base: main
          token: ${{ secrets.DEVOPS_DIRECTIVE_KUBERNETES_COURSE_GITHUB_ACTION_PAT }}
          title: "Update image tags to (${{ needs.generate-image-tag.outputs.image_tag }})"
